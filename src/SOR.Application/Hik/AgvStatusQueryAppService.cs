using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore.Internal;
using Microsoft.Extensions.Caching.Distributed;
using SOR.AGVTasks;
using SOR.Flags;
using SOR.Hik;
using SOR.Zones;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using Volo.Abp.Caching;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.ObjectMapping;
using static SOR.Permissions.SORPermissions;

namespace SOR.Hik
{
    [AllowAnonymous]
    public class AgvStatusQueryAppService : ApplicationService, IAgvStatusQueryAppService
    {
        private readonly IDistributedCache<List<AGVStatusDto>> _cache;
        private readonly IDistributedCache<ZoneDto> _cacheZone;
        private readonly IRepository<Zone, Guid> _zoneRepository;
        private readonly IRepository<Flag, Guid> _flagRepository;
        private readonly IRepository<AGVTask, Guid> _agvTaskRepository;

        public AgvStatusQueryAppService(IDistributedCache<List<AGVStatusDto>> cache, IDistributedCache<ZoneDto> cacheZone, IRepository<Zone, Guid> zoneRepository, IRepository<AGVTask, Guid> agvTaskRepository, IRepository<Flag, Guid> flagRepository)
        {
            _cache = cache;
            _cacheZone = cacheZone;
            _zoneRepository = zoneRepository;
            _agvTaskRepository = agvTaskRepository;
            _flagRepository = flagRepository;
        }

        public async Task<List<AGVStatusDto>> GetAllAsync(string zoneId)
        {
            List<AGVStatusDto> aGVStatusDtos = new List<AGVStatusDto>();

            var agvStatusList = await _cache.GetAsync("AGVStatus");
            if (agvStatusList == null)
            {
                return aGVStatusDtos;
            }

            if (zoneId == "all")
            {
                aGVStatusDtos = agvStatusList.ToList();
            }
            else
            {
                var zoneInfo = await _cacheZone.GetAsync($"Zone:{zoneId}");
                if (zoneInfo == null)
                {
                    var zone = await _zoneRepository.GetAsync(z => z.Id.ToString() == zoneId);
                    zoneInfo = ObjectMapper.Map<Zone, ZoneDto>(zone);
                    if (zoneInfo == null)
                    {
                        // Nếu không tìm được zone => trả về danh sách rỗng
                        return aGVStatusDtos;
                    }
                }

                aGVStatusDtos = agvStatusList
                .Where(r => r.posX <= zoneInfo.Xmax && r.posX >= zoneInfo.Xmin
                         && r.posY <= zoneInfo.Ymax && r.posY >= zoneInfo.Ymin)
                .ToList();
            }

            return aGVStatusDtos;
        }


        public async Task<AndonDetailDto> GetStatusTask(AGVStatusDto robot)
        {
            AndonDetailDto andonDetailDto = new AndonDetailDto() 
            {
                Status = robot.status,
                PosX = robot.posX,
                PosY = robot.posY,
                Battery = robot.battery,
                RobotIp = robot.robotIp
            };

            var listTask = await _agvTaskRepository.GetQueryableAsync();
            var listFlag = await _flagRepository.GetQueryableAsync();
            var agvTask = listTask.Where(r => r.AGVID.Equals(robot.robotCode)).OrderByDescending(r => r.CreationTime).FirstOrDefault();
            andonDetailDto.AgvTask = ObjectMapper.Map<AGVTask, AGVTaskDto>(agvTask) ?? new AGVTaskDto();
            andonDetailDto.ListFlag = FindSurroundingPoints(listFlag.ToList(), robot.posX, robot.posY);

            return andonDetailDto;
        }

        public List<string> FindSurroundingPoints(List<Flag> points, double x1, double y1)
        {
            Flag left = null, right = null, top = null, bottom = null;
            double leftDist = double.MaxValue, rightDist = double.MaxValue;
            double topDist = double.MaxValue, bottomDist = double.MaxValue;

            foreach (var p in points)
            {
                double dist = Math.Sqrt(Math.Pow(p.X - x1, 2) + Math.Pow(p.Y - y1, 2));

                if (p.X < x1 && dist < leftDist)
                {
                    left = p;
                    leftDist = dist;
                }
                if (p.X > x1 && dist < rightDist)
                {
                    right = p;
                    rightDist = dist;
                }
                if (p.Y > y1 && dist < topDist)
                {
                    top = p;
                    topDist = dist;
                }
                if (p.Y < y1 && dist < bottomDist)
                {
                    bottom = p;
                    bottomDist = dist;
                }
            }

            var result = new List<string>();
            if (left != null) result.Add(left.FlagName);
            if (right != null) result.Add(right.FlagName);
            if (top != null) result.Add(top.FlagName);
            if (bottom != null) result.Add(bottom.FlagName);

            return result;
        }

    }
}
