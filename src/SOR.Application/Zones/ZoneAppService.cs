using Microsoft.AspNetCore.Authorization;
using SOR.Hik;
using SOR.Permissions;
using SOR.Zones;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Caching;
using Volo.Abp.Domain.Repositories;

namespace SOR.Zones
{
    [Authorize]
    public class ZoneAppService :
        CrudAppService<
            Zone, // Entity
            ZoneDto, // DTO
            Guid, // Primary key
            GetZoneInputDto, // Get list input
            CreateUpdateZoneDto>, // Create/update input
        IZoneAppService
    {
        private readonly IDistributedCache<ZoneDto> _cache;

        public ZoneAppService(
            IRepository<Zone, Guid> repository,
            IDistributedCache<ZoneDto> cache)
            : base(repository)
        {
            _cache = cache;
            GetPolicyName = SORPermissions.Zones.Default;
            GetListPolicyName = SORPermissions.Zones.Default;
            CreatePolicyName = SORPermissions.Zones.Create;
            UpdatePolicyName = SORPermissions.Zones.Update;
            DeletePolicyName = SORPermissions.Zones.Delete;
        }

        public override async Task<PagedResultDto<ZoneDto>> GetListAsync(GetZoneInputDto input)
        {
            var query = await Repository.GetQueryableAsync();

            if (!string.IsNullOrWhiteSpace(input.ZoneName))
                query = query.Where(x => x.ZoneName.Contains(input.ZoneName));

            var totalCount = await AsyncExecuter.CountAsync(query);
            var items = await AsyncExecuter.ToListAsync(query.OrderBy(input.Sorting ?? "ZoneName").PageBy(input));

            return new PagedResultDto<ZoneDto>(
                totalCount,
                ObjectMapper.Map<List<Zone>, List<ZoneDto>>(items));
        }

        public override async Task<ZoneDto> CreateAsync(CreateUpdateZoneDto input)
        {
            input.ZoneName = input.ZoneName.Trim();
            var checkCode = await Repository.FirstOrDefaultAsync(r => r.ZoneName.Equals(input.ZoneName));
            if (checkCode != null)
            {
                throw new UserFriendlyException("Tên khu vực đã tồn tại.");
            }
            var entity = ObjectMapper.Map<CreateUpdateZoneDto, Zone>(input);
            await Repository.InsertAsync(entity, autoSave: true);

            var dto = ObjectMapper.Map<Zone, ZoneDto>(entity);
            await _cache.SetAsync($"Zone:{entity.Id}", dto); 

            return dto;
        }

        public override async Task<ZoneDto> UpdateAsync(Guid id, CreateUpdateZoneDto input)
        {
            input.ZoneName = input.ZoneName.Trim();
            var checkCode = await Repository.FirstOrDefaultAsync(r =>
                        r.ZoneName.Equals(input.ZoneName) && r.Id != id);

            if (checkCode != null)
            {
                throw new UserFriendlyException("Tên khu vực đã tồn tại.");
            }
            var entity = await Repository.GetAsync(id);
            ObjectMapper.Map(input, entity);
            await Repository.UpdateAsync(entity, autoSave: true);

            var dto = ObjectMapper.Map<Zone, ZoneDto>(entity);
            await _cache.SetAsync($"Zone:{id}", dto); 

            return dto;
        }

        public override async Task DeleteAsync(Guid id)
        {
            await Repository.DeleteAsync(id);
            await _cache.RemoveAsync($"Zone:{id}"); 
        }

        [AllowAnonymous]
        public async Task<List<ZoneDto>> GetZoneDtosAsync()
        {
            var query = await Repository.GetQueryableAsync();
            var items = await AsyncExecuter.ToListAsync(
                query.OrderBy(x => x.ZoneName));

            return ObjectMapper.Map<List<Zone>, List<ZoneDto>>(items);
        }

    }
}
