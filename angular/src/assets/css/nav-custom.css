
.lpx-sidebar {
  background-color: oklch(1 0 0);
  color: #9198a5;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  height: 100%;
}
.lpx-sidebar .lpx-logo-container {
  position: fixed;
  width: 280px;
  z-index: 100;
  background-color: oklch(1 0 0);
  top: 0;
}
.lpx-sidebar .lpx-logo-container .menu-collapse-icon {
  position: absolute;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.5em;
  cursor: pointer;
}
.lpx-sidebar a {
  text-decoration: none;
}

.lpx-sidebar-container {
  min-width: 280px;
  z-index: 1000;
  position: fixed;
  top: 0;
  height: 100vh;
}
.lpx-sidebar-container .lpx-sidebar {
  min-height: 100vh;
}
.lpx-sidebar-container .lpx-sidebar .lpx-nav {
  min-width: 280px;
  height: 100vh;
}
.lpx-sidebar-container .lpx-sidebar .lpx-nav .lpx-nav-menu {
  background-color: oklch(1 0 0);
  max-width: 280px;
}

.lpx-nav {
  color: #9198a5;
}
.lpx-nav .lpx-nav-menu {
  padding: 0 0 15px 0;
}

.lpx-nav-menu {
  margin: 0;
  padding: 0;
}
.lpx-nav-menu a {
  text-decoration: none;
}
.lpx-nav-menu .outer-menu-item.filter-hidden,
.lpx-nav-menu .lpx-inner-menu.collapsed {
  display: none;
}
.lpx-nav-menu .lpx-menu-item-link {
  display: flex;
  width: 100%;
  cursor: pointer;
  color: oklch(27.4% 0.006 286.033);
  height: 42px;
  line-height: 42px;
  transition: color 0.25s ease, background-color 0.3s ease;
  margin: 1px 0;
  padding: 0 18px;
  position: relative;
}
.lpx-nav-menu .lpx-menu-item-link:hover {
  color: oklch(27.4% 0.006 286.033);
}
.lpx-nav-menu .lpx-menu-item-link:hover .lpx-menu-item-icon {
  color: var(--primary);
  opacity: 1;
}
.lpx-nav-menu .lpx-menu-item-link:hover .dd-icon {
  color:  var(--primary);
  opacity: 1;
}
.lpx-nav-menu .lpx-menu-item-link.selected, .lpx-nav-menu .lpx-menu-item-link.expanded {
  color:  var(--primary);
  font-weight: 600;
  background-color: color-mix(in oklab, var(--accent) 60%, transparent)
}
.lpx-nav-menu .lpx-menu-item-link.selected .lpx-menu-item-icon,
.lpx-nav-menu .lpx-menu-item-link.selected .dd-icon, .lpx-nav-menu .lpx-menu-item-link.expanded .lpx-menu-item-icon,
.lpx-nav-menu .lpx-menu-item-link.expanded .dd-icon {
  color: var(--primary);
  opacity: 1;
}
.lpx-nav-menu .lpx-menu-item-link .lpx-menu-item-icon .lpx-icon {
  text-align: center;
  display: inline-block;
  position: relative;
  width: 36px;
  transition: all 0.25s ease;
  font-size: 16px;
  opacity: 0.86;
}
.lpx-nav-menu .lpx-menu-item-link .dd-icon {
  position: absolute;
  text-align: center;
  opacity: 0.5;
  right: 24px;
  width: 24px;
}
.lpx-nav-menu .lpx-menu-item-link .lpx-menu-item-text {
  white-space: wrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 178px;
  font-size: 1.1em;
}
.lpx-nav-menu .lpx-inner-menu-item .lpx-menu-item-link {
  height: 36px;
  line-height: 36px;
  padding-left: 54px;
  transition: background-color 0.5s ease, color 0.25s ease;
}
.lpx-nav-menu .lpx-inner-menu-item .lpx-menu-item-link.selected, .lpx-nav-menu .lpx-inner-menu-item .lpx-menu-item-link:hover {
  background-color: oklch(1 0 0);
}
.lpx-nav-menu .lpx-inner-menu-item .lpx-menu-item-icon {
  margin-right: 8px;
}
.lpx-nav-menu .lpx-inner-menu-item .lpx-menu-item-icon .lpx-icon {
  width: 16px;
}
.lpx-nav-menu .lpx-inner-menu-item .lpx-menu-item-text {
  width: 154px;
  font-size: 1.075em;
}
.lpx-nav-menu .lpx-inner-menu {
  margin: 0;
  padding: 0;
}
.lpx-nav-menu .lpx-inner-menu .lpx-inner-menu .lpx-menu-item-link {
  padding-left: 78px;
}
.lpx-nav-menu .group-menu-item {
  padding: 0.5rem 0 0.5rem 1rem;
  font-weight: 500;
}

.lpx-menu-item {
  cursor: pointer;
  color: #9198a5;
  height: 36px;
  line-height: 36px;
  transition: color 0.25s ease, background-color 0.3s ease;
}
.lpx-menu-item:hover {
  color: #fff;
}
.lpx-menu-item:hover .lpx-menu-item-icon {
  color: var(--lpx-brand);
  opacity: 1;
}
.lpx-menu-item:hover .dd-icon {
  color: #fff;
  opacity: 1;
}
.lpx-menu-item.selected {
  color: #fff;
  background-color: oklch(1 0 0);
}
.lpx-menu-item.selected .lpx-menu-item-icon,
.lpx-menu-item.selected .dd-icon {
  color: var(--lpx-brand);
  opacity: 1;
}
.lpx-menu-item .dd-icon {
  position: absolute;
  text-align: center;
  opacity: 0.5;
  right: 24px;
  width: 24px;
}

.lpx-inner-menu-item .lpx-menu-item-link {
  transition: background-color 0.5s ease, color 0.25s ease;
}
.lpx-inner-menu-item .lpx-menu-item-link.selected, .lpx-inner-menu-item .lpx-menu-item-link:hover {
  background-color: oklch(1 0 0);
}

.lpx-nav-menu .lpx-inner-menu-item .lpx-menu-item-link {
  height: 32px;
  line-height: 32px;
  padding-left: 54px;
  transition: background-color 0.5s ease, color 0.25s ease;
}