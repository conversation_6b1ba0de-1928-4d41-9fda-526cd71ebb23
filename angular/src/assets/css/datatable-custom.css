.ngx-datatable.material {
  background-color: #ffffff;
  border-radius: 0.5rem;
  color: #686b6e;
}
.ngx-datatable.material .datatable-header {
  background-color: #ffffff;
  border-bottom: 0;
  width: 100% !important;
  height:  calc(var(--spacing) * 10);
}
.ngx-datatable.material .datatable-header .datatable-header-cell {
  background-color: var(--primary);
  padding-left: 1rem !important;
  padding-right: 1rem !important;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-weight: 500 !important;
  color: #fff;
  font-size: 0.95em !important;
  letter-spacing: 0.75px;
}
.ngx-datatable.material .datatable-header .datatable-header-cell .datatable-icon-sort-unset:before {
  font-family: bootstrap-icons !important;
  content: "\f282" !important;
  margin: 0 4px;
}
.ngx-datatable.material .datatable-header .datatable-header-cell .datatable-icon-down:before {
  color: var(--lpx-brand);
  font-family: bootstrap-icons !important;
  content: "\f286" !important;
  margin: 0 4px;
}
.ngx-datatable.material .datatable-header .datatable-header-cell .datatable-icon-up:before {
  color: var(--lpx-brand);
  font-family: bootstrap-icons !important;
  content: "\f282" !important;
  margin: 0 4px;
}
.ngx-datatable.material .datatable-header .resize-handle {
  border-right: 0 !important;
}
.ngx-datatable.material .datatable-body {
  width: 100% !important;
}
.ngx-datatable.material .datatable-body .datatable-body-row {
  background-color: #ffffff;
}
.ngx-datatable.material .datatable-body .datatable-body-row .datatable-body-cell {
  padding: 0.875rem 1rem !important;
  vertical-align: middle;
  border-top: 1px solid #e7e9ec !important;
  color: #686b6e;
}
.ngx-datatable.material .datatable-body .datatable-body-row .datatable-body-cell .btn {
  padding: 0.375rem 0.75rem;
  font-size: 0.8em;
}
.ngx-datatable.material .datatable-footer {
  background-color: #ffffff;
  color: rgba(34, 35, 36, 0.5) !important;
  font-weight: 500;
  border-top: 1px solid #e7e9ec;
}


.ngx-datatable.material .datatable-footer .datatable-pager li.active a {
    background-color: var(--primary);
    color: #fff;
    font-weight: normal;
}