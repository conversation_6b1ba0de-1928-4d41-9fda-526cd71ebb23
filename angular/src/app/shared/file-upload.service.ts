import { Injectable } from '@angular/core';
import { RestService } from '@abp/ng.core';
import { Observable } from 'rxjs';

@Injectable({
providedIn: 'root',
})
export class FileUploadService {
    apiName = 'Default'; // hoặc tên remote service

    constructor(private restService: RestService) {}

    upload(file: File): Observable<any> {
        const formData = new FormData();
        formData.append('file', file);
        return this.restService.request({
            method: 'POST',
            url: '/api/app/file/upload',
            body: formData,
        });
    }
}