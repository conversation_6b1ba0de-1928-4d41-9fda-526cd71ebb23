import { ListService, PagedResultDto } from '@abp/ng.core';
import { Component, OnInit } from '@angular/core';
import { ViewTaskCache, ViewTaskCacheService } from '../proxy/agvtasks';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { NgbDateNativeAdapter, NgbDateAdapter } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationService, Confirmation } from '@abp/ng.theme.shared';
import { DatePipe } from '@angular/common';
import { timer } from 'rxjs';
import { ToasterService } from '@abp/ng.theme.shared';
import { OperationTaskService } from '../proxy/hik';

@Component({
  selector: 'app-view-task',
  templateUrl: './view-task.component.html',
  styleUrl: './view-task.component.scss',
  standalone: false,
  providers: [ListService],
})
export class ViewTaskComponent implements OnInit {
  viewTask: ViewTaskCache[] = [];
  constructor(
    private taskService: ViewTaskCacheService, 
    private toaster: ToasterService,
    private operationService: OperationTaskService,
    private confirmation: ConfirmationService,
  ) {

  }

ngOnInit(): void {
    this.loadTasks();
    setInterval(() => this.loadTasks(), 30000); // reload mỗi 30s
  }

  loadTasks(): void {
    this.taskService.getTask().subscribe((data) => {
      this.viewTask = data;
    });
  }

  getTaskStatusClass(status: string): string {
  switch (status) {
    case 'Created':
    case '1':
      return 'badge bg-secondary'; // xám
    // case 'Executing':
    // case '2':
    //   return 'badge bg-warning text-dark'; // vàng
    // case 'Sending':
    // case '3':
    //   return 'badge bg-primary'; // xanh dương
    case 'Success':
    case '9':
      return 'badge bg-success'; // xanh lá
    case '0':
    case '4':
    case '5':
      return 'badge bg-danger'; // đỏ
    default:
      return 'badge bg-warning text-dark'; // mặc định
  }
}

  getAgvStatusClass(status: string): string {
  switch (status) {
    case '3':
    case '4':
      return 'badge bg-secondary'; // xám
    case '1':
      return 'badge bg-success'; // xanh lá
    case '2':
    case '6':
    case '7':
    case '8':
      return 'badge bg-warning text-dark'; // đỏ
    default:
      return 'badge bg-danger'; // mặc định
  }
}

getAgvImage(type: string): string {
  switch (type?.toUpperCase()) {
    case 'BOX':
      return 'assets/images/agv/box.png';
    case 'PALLET':
      return 'assets/images/agv/pallet.png';
    case 'TROLLEY':
      return 'assets/images/agv/trolley.png';
    default:
      return 'assets/icons/agv.svg'; // ảnh mặc định nếu không khớp
  }
}

    isTaskStatusContinue(status: string | number): boolean {
      const allowedStatuses = [0, 4, 5];
      return allowedStatuses.includes(+status); // đảm bảo ép kiểu
    }

    onComplete(row: any): void {
      this.operationService.complateTask(row.taskId).subscribe({
        next: () => {
          this.toaster.success('SOR::ActionSuccess', 'SOR::Success');
          this.loadTasks();
        }
      });
    }

    onContinue(row: any): void {
            this.operationService.continueTask(row.taskId).subscribe({
        next: () => {
          this.toaster.success('SOR::ActionSuccess', 'SOR::Success');
          this.loadTasks();
        }
      });
    }

    onCancel(row: any): void {
      this.confirmation.warn('::AreYouSureToCancel', '::AreYouSure').subscribe((status) => {
        if (status === Confirmation.Status.confirm) {
          this.operationService.cancelTask(row.taskId).subscribe({
          next: () => {
            this.toaster.success('SOR::ActionSuccess', 'SOR::Success');
            this.loadTasks();
          }
        });
        }
      });       
    }
}

