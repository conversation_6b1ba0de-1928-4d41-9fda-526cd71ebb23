import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ViewTaskRoutingModule } from './view-task-routing.module';
import { ViewTaskComponent } from './view-task.component';
import { SharedModule } from '../shared/shared.module';
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';


@NgModule({
  declarations: [
    ViewTaskComponent,
  ],
  imports: [
    CommonModule,
    ViewTaskRoutingModule,
    SharedModule,
    NgbDatepickerModule
  ]
})
export class ViewTaskModule { }
