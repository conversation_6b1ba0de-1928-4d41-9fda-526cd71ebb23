<div class="task-container" *ngIf="viewTask.length > 0; else emptyState">
  <div *ngFor="let row of viewTask" class="task-card">
    <div class="task-left">
      <img [src]="getAgvImage(row.type)" alt="AGV" class="agv-type-img" />
    </div>

    <div class="task-content">
      <div class="task-title">{{ row.taskId }}</div>
      <div class="task-meta">
        <div>{{ '::Router' | abpLocalization }}: <strong>{{ row.routerName }}</strong></div>
        <div>{{ '::AGVCode' | abpLocalization }}: <strong>{{ row.agvCode }}</strong></div>
        <div *ngIf="row.taskStatus !== '5'">
          {{ '::AGVStatus' | abpLocalization }}: 
          <span class="status" [ngClass]="getAgvStatusClass(row.taskStatus)">
            {{ ('::Enum:AGVStatus.' + row.agvStatus) | abpLocalization }}
          </span>
        </div>
        <div>
          {{ '::TaskStatus' | abpLocalization }}:
          <span class="status" [ngClass]="getTaskStatusClass(row.taskStatus)">
            {{ ('::Enum:AgvTaskStatus.' + row.taskStatus) | abpLocalization }}
          </span>
        </div>
      </div>
    </div>

    <div class="task-right">
      <div class="created-at">
        {{ row.createAt | date: 'dd/MM/yyyy HH:mm:ss': 'dd/MM/yyyy HH:mm' }}
      </div>
      <!-- Nút hành động -->
      <div class="task-actions mt-2">
        <button *ngIf="row.taskStatus === '5'" class="btn btn-success btn-sm me-1"
                (click)="onComplete(row)">
          <i class="fas fa-check"></i> {{ '::Complete' | abpLocalization }}
        </button>

        <!-- <button *ngIf="isTaskStatusContinue(row.taskStatus)"
                class="btn btn-primary btn-sm me-1"
                (click)="onContinue(row)">
          🔄 {{ '::Continue' | abpLocalization }}
        </button> -->

        <button *ngIf="!isTaskStatusContinue(row.taskStatus)"
                class="btn btn-danger btn-sm"
                (click)="onCancel(row)">
          <i class="fas fa-times"></i> {{ '::Cancel' | abpLocalization }}
        </button>
      </div>
    </div>
  </div>
</div>
<!-- Ngược lại, nếu không có đơn -->
<ng-template #emptyState>
  <div class="text-center mt-5 text-muted">
    <img src="assets/images/getting-started/no-data.svg" alt="No Orders" style="width: 120px; opacity: 0.6;" />
    <h5 class="mt-3">{{ '::NoOrderToday' | abpLocalization }}</h5>
    <p>{{ '::NoAgvTaskToday' | abpLocalization }}</p>
  </div>
</ng-template>
