.badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.85rem;
  font-weight: 500;
  display: inline-block;
}

.bg-success { background-color: #198754; color: white; }
.bg-danger { background-color: #dc3545; color: white; }
.bg-warning { background-color: #ffc107; color: black; }
.bg-primary { background-color: #0d6efd; color: white; }
.bg-secondary { background-color: #6c757d; color: white; }
.bg-light { background-color: #f8f9fa; color: #000; }

.task-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.task-card {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  padding: 1rem;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
}

.task-left img {
  width: 100px;
  height: 100px;
  object-fit: contain;
  margin-right: 1rem;
}

.task-content {
  flex-grow: 1;
}

.task-title {
  font-weight: bold;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.task-meta {
  font-size: 0.9rem;
  color: #555;
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.status {
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.8rem;
  margin-left: 4px;
}

.status.running {
  background: #d1e7dd;
  color: #0f5132;
}

.status.completed {
  background: #cfe2ff;
  color: #084298;
}

.task-right {
  text-align: right;
  min-width: 140px;
}

.created-at {
  font-size: 0.85rem;
  color: #888;
}

.text-muted {
  color: #888 !important;
}
