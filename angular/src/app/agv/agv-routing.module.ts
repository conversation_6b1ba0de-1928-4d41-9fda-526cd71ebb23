import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AGVComponent } from './agv.component';
import { authGuard, permissionGuard } from '@abp/ng.core';

const routes: Routes = [{ path: '', component: AGVComponent, canActivate: [authGuard, permissionGuard]}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AGVRoutingModule { }
