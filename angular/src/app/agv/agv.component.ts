import { ListService, PagedResultDto } from '@abp/ng.core';
import { Component, OnInit } from '@angular/core';
import { AGVDto, AGVService } from '../proxy/agvs';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { NgbDateNativeAdapter, NgbDateAdapter } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationService, Confirmation } from '@abp/ng.theme.shared';
import { ToasterService } from '@abp/ng.theme.shared';

@Component({
  selector: 'app-agv',
  templateUrl: './agv.component.html',
  styleUrls: ['./agv.component.scss'],
  standalone: false,
  providers: [ListService],
})
export class AGVComponent implements OnInit {
  agv = { items: [], totalCount: 0 } as PagedResultDto<AGVDto>;
  selectedAGV = {} as AGVDto;
  form: FormGroup;
  filterForm: FormGroup;
  //agvStatusOptions = agvStatusOptions;
  isModalOpen = false;
statusOptions = [
'','0', '1'
];

statusCreateOptions = [
'0', '1'
];

  constructor(
    public readonly list: ListService,
    private fb: FormBuilder,
    private agvService: AGVService,
    private confirmation: ConfirmationService,
    private toaster: ToasterService
  ) {
    // Khởi tạo form lọc
    this.filterForm = this.fb.group({
      agvCode: [''],
      operationStatus: [''],
    });

    // Kết nối dữ liệu với bộ lọc
    this.list.hookToQuery((query) => this.getData(query)).subscribe((res) => {
      this.agv = res;
    });
  }

  ngOnInit(): void {
    console.log('AGVComponent initialized');
  }

  getData(query) {
    const filter = this.filterForm.value;

    return this.agvService.getList({
      ...query,
      agvCode: filter.agvCode || undefined,
      operationStatus: filter.operationStatus || undefined,
    });
  }

  search() {
    this.list.get();
  }

  resetFilters() {
    this.filterForm.reset();
    this.list.get();
  }

  createAGV() {
    this.selectedAGV = {} as AGVDto;
    this.buildForm();
    this.isModalOpen = true;
  }

  editAGV(id: string) {
    this.agvService.get(id).subscribe((agv) => {
      this.selectedAGV = agv;
      this.buildForm();
      this.isModalOpen = true;
    });
  }

  buildForm() {
    this.form = this.fb.group({
      operationStatus: [this.selectedAGV.operationStatus || '', Validators.required],
      agvCode: [this.selectedAGV.agvCode || '', Validators.required],
      description: [this.selectedAGV.description || ''],
    });
  }

  save() {
    if (this.form.invalid) return;

    const request = this.selectedAGV.id
      ? this.agvService.update(this.selectedAGV.id, this.form.value)
      : this.agvService.create(this.form.value);

    request.subscribe(() => {
      this.isModalOpen = false;
      this.form.reset();
      this.list.get();
    });
  }

  deleteAGV(id: string) {
    this.confirmation.warn('::AreYouSureToDelete', '::AreYouSure').subscribe((status) => {
      if (status === Confirmation.Status.confirm) {
        this.agvService.delete(id).subscribe(() => this.list.get());
      }
    });
  }

  selectedFile?: File;

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input?.files?.length) {
    this.selectedFile = input.files[0];
    }
  }

  onUpload() {
    if (!this.selectedFile) return;
    const formData = new FormData();
    formData.append('file', this.selectedFile);

    this.agvService.upload(formData).subscribe({
      next: () => {
        this.toaster.success('SOR::SaveSuccess', 'SOR::Success');
        this.list.get();
      },
      error: (err) => {
        this.toaster.error('SOR::SaveError', 'SOR::Error');
      }
    });
  }

  downloadSample() {
  this.agvService.download().subscribe({
    next: (blob) => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'sample-agv.xlsx';
      a.click();
      window.URL.revokeObjectURL(url);
    },
    error: (err) => {
      console.error('Lỗi khi tải file mẫu:', err);
    }
  });
}

}
