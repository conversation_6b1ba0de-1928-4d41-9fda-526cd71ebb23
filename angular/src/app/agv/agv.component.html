


<div class="card">
  <div class="card-header router-header">
    <div class="row">
      <div class="col col-md-6">
        <h5 class="card-title">{{ '::Menu:AGV' | abpLocalization }}</h5>
      </div>
      <div class="col-md-6 text-end d-flex justify-content-end gap-2 flex-wrap pt-2">
        <button *abpPermission="'SOR.AGVs.Create'" id="create" class="btn btn-primary" type="button" (click)="createAGV()">
          <i class="fa fa-plus me-1"></i>
          <span>{{ "::Create" | abpLocalization }}</span>
        </button>

        <a class="btn btn-outline-secondary btn-sm d-flex align-items-center" (click)="downloadSample()">
          <i class="fa fa-download me-1"></i> T<PERSON>i mẫu
        </a>

        <form (ngSubmit)="onUpload()" #uploadForm="ngForm" class="d-flex align-items-center gap-2 upload-form">
          <input type="file" class="form-control form-control-sm" (change)="onFileSelected($event)" accept=".xlsx" required />
          <button class="btn btn-secondary btn-sm" type="submit" [disabled]="!selectedFile">
            <i class="fa fa-upload me-1"></i> Import
          </button>
        </form>
      </div>
    </div>
  </div>

  <div class="card-body">
    
<form [formGroup]="filterForm" (ngSubmit)="search()" class="mb-3 row align-items-end">
  <div class="col-md-3">
    <label>{{ '::AGVCode' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="agvCode" />
  </div>
  <div class="col-md-3">
    <label>{{ '::OperationStatus' | abpLocalization }}</label>
    <select class="form-control" formControlName="operationStatus">
      <option *ngFor="let status of statusOptions" [ngValue]="status">
        {{ status === '' ? ('::All' | abpLocalization) : ('::Enum:Status.' + status) | abpLocalization }}
      </option>
    </select>
  </div>
  <div class="col-md-4"></div>
  <div class="col-md-2 d-flex gap-2">
    <button class="btn btn-primary btn-sm w-auto" type="submit">
      <i class="fa fa-search me-1"></i>{{ '::Search' | abpLocalization }}
    </button>
    <button class="btn btn-secondary btn-sm w-auto" type="button" (click)="resetFilters()">
      <i class="fa fa-times me-1"></i>{{ '::Clear' | abpLocalization }}
    </button>
  </div>
</form>
    <ngx-datatable
      [rows]="agv.items"
      [count]="agv.totalCount"
      [list]="list" default
       [columnMode]="'force'"
      >

      <!-- Actions Column -->
      <ngx-datatable-column
        [name]="'::Actions' | abpLocalization"
        [maxWidth]="150"
        [sortable]="false"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div ngbDropdown container="body" class="d-inline-block">
            <button class="btn btn-primary btn-sm dropdown-toggle" ngbDropdownToggle>
              <i class="fa fa-cog me-1"></i>{{ '::Actions' | abpLocalization }}
            </button>
            <div ngbDropdownMenu>
              <button *abpPermission="'SOR.AGVs.Update'" ngbDropdownItem (click)="editAGV(row.id)">
                {{ '::Edit' | abpLocalization }}
              </button>
              <button *abpPermission="'SOR.AGVs.Delete'" ngbDropdownItem (click)="deleteAGV(row.id)">
                {{ '::Delete' | abpLocalization }}
              </button>
            </div>
          </div>
        </ng-template>
      </ngx-datatable-column>

      <ngx-datatable-column
        [name]="'::AGVCode' | abpLocalization"
        [flexGrow]="1" prop="agvCode" [maxWidth]="200"
      ></ngx-datatable-column>
      
      <ngx-datatable-column
        [name]="'::OperationStatus' | abpLocalization"
        [flexGrow]="1" prop="operationStatus" [maxWidth]="200"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ ('::Enum:Status.' + row.operationStatus) | abpLocalization }}
        </ng-template>
      </ngx-datatable-column>

      <ngx-datatable-column
        [name]="'::Description' | abpLocalization"
        [flexGrow]="1" prop="description"
      ></ngx-datatable-column>


    </ngx-datatable>

  </div>
</div>

<!-- Modal -->
<abp-modal [(visible)]="isModalOpen">
  <ng-template #abpHeader>
    <h3>{{ (selectedAGV?.id ? '::Permission:AGVs.Update' : '::Permission:AGVs.Create') | abpLocalization }}</h3>
  </ng-template>

    <ng-template #abpBody>
      <form [formGroup]="form" (ngSubmit)="save()">
        <div class="mt-2">
          <label for="agv-code">{{ '::AGVCode' | abpLocalization }}</label><span> * </span>
          <input
            type="text"
            id="agv-code"
            class="form-control"
            formControlName="agvCode"
            autofocus
          />
        </div>

        <div class="mt-2">
          <label for="operation-status">{{'::OperationStatus' | abpLocalization}}</label><span> * </span>
          <select
            class="form-control"
            id="operation-status"
            formControlName="operationStatus"
          >
            <option
              *ngFor="let status of statusCreateOptions"
              [ngValue]="status"
            >
              {{ ('::Enum:Status.' + status) | abpLocalization }}
            </option>
          </select>
        </div>

        <div class="mt-2">
          <label for="error-message">{{'::Description' | abpLocalization}}</label>
          <textarea
            id="error-message"
            class="form-control"
            rows="3"
            formControlName="description"
          ></textarea>
        </div>
      </form>
    </ng-template>


  <ng-template #abpFooter>
    <button type="button" class="btn btn-secondary" abpClose>
      {{ '::Close' | abpLocalization }}
    </button>
    <button class="btn btn-primary" (click)="save()" [disabled]="form.invalid">
      <i class="fa fa-check mr-1"></i>
      {{ '::Save' | abpLocalization }}
    </button>
  </ng-template>
</abp-modal>
