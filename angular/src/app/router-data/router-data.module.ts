import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { RouterDataRoutingModule } from './router-data-routing.module';
import { RouterDataComponent } from './router-data.component';
import { SharedModule } from '../shared/shared.module';
import { ContextMenuService, TreeGridModule } from '@syncfusion/ej2-angular-treegrid';
import { TabModule, TreeViewModule } from '@syncfusion/ej2-angular-navigations';
import { EditService, FilterService, GridModule, PageService, SortService, } from '@syncfusion/ej2-angular-grids';


@NgModule({
  declarations: [
    RouterDataComponent,
  ],
  imports: [
    CommonModule,
    RouterDataRoutingModule,
    SharedModule,
    TreeGridModule,
    GridModule,
    TabModule,
    TreeViewModule,
  ],
  providers: [SortService, PageService, FilterService, ContextMenuService, EditService],
})
export class RouterDataModule { }
