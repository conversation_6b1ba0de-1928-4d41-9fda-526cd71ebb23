


<div class="card">
  <div class="card-header router-header">
    <div class="row align-items-center">
      <div class="col-md-6">
        <h5 class="card-title mb-0">{{ '::Menu:RouterData' | abpLocalization }}</h5>
      </div>
      <div class="col-md-6 text-end d-flex justify-content-end gap-2 flex-wrap pt-2">
        <button *abpPermission="'SOR.RouterDatas.Create'" id="create" class="btn btn-primary" type="button" (click)="createRouterData()">
          <i class="fa fa-plus me-1"></i>
          <span>{{ "::Create" | abpLocalization }}</span>
        </button>

        <form (ngSubmit)="onUpload()" #uploadForm="ngForm" class="d-flex align-items-center gap-2 upload-form">
          <input type="file" class="form-control form-control-sm" (change)="onFileSelected($event)" accept=".json" required />
          <button class="btn btn-secondary btn-sm" type="submit" [disabled]="!selectedFile">
            <i class="fa fa-upload me-1"></i> Upload
          </button>
        </form>
      </div>
    </div>
  </div>


  <div class="card-body">
    
<form [formGroup]="filterForm" (ngSubmit)="search()" class="mb-3 row align-items-end">
  <div class="col-md-3">
    <label>{{ '::RouterName' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="routerName" />
  </div>
  <div class="col-md-3">
    <label>{{ '::RouterType' | abpLocalization }}</label>
    <select class="form-control" formControlName="routerType">
      <option *ngFor="let status of statusOptions" [ngValue]="status">
        {{ status === '' ? ('::All' | abpLocalization) : (status) | abpLocalization }}
      </option>
    </select>
  </div>
  <div class="col-md-3">
    <label>{{ '::Router' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="router" />
  </div>
  <div class="col-md-1"></div>
  <div class="col-md-2 text-end d-flex gap-2">
    <button class="btn btn-primary btn-sm w-auto" type="submit">
      <i class="fa fa-search me-1"></i>{{ '::Search' | abpLocalization }}
    </button>
    <button class="btn btn-secondary btn-sm w-auto" type="button" (click)="resetFilters()">
      <i class="fa fa-times me-1"></i>{{ '::Clear' | abpLocalization }}
    </button>
  </div>
</form>
    <ngx-datatable
      [rows]="routerData.items"
      [count]="routerData.totalCount"
      [list]="list" default
       [columnMode]="'force'"
    >

          <!-- Actions Column -->
      <ngx-datatable-column
        [name]="'::Actions' | abpLocalization"
        [maxWidth]="150"
        [sortable]="false"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div ngbDropdown container="body" class="d-inline-block">
            <button class="btn btn-primary btn-sm dropdown-toggle" ngbDropdownToggle>
              <i class="fa fa-cog me-1"></i>{{ '::Actions' | abpLocalization }}
            </button>
            <div ngbDropdownMenu>
              <button *abpPermission="'SOR.RouterDatas.Update'" ngbDropdownItem (click)="editRouterData(row.id)">
                {{ '::Edit' | abpLocalization }}
              </button>
              <button *abpPermission="'SOR.RouterDatas.Delete'" ngbDropdownItem (click)="deleteRouterData(row.id)">
                {{ '::Delete' | abpLocalization }}
              </button>
            </div>
          </div>
        </ng-template>
      </ngx-datatable-column>

      <ngx-datatable-column
        [name]="'::RouterName' | abpLocalization"
        [flexGrow]="1" prop="routerName" [maxWidth]="250"
      ></ngx-datatable-column>
      
      <ngx-datatable-column
        [name]="'::RouterType' | abpLocalization"
        [flexGrow]="1" prop="routerType" [maxWidth]="150"
      ></ngx-datatable-column>


      <ngx-datatable-column
        [name]="'::RouterContent' | abpLocalization"
        [flexGrow]="1" prop="contentData"
      ></ngx-datatable-column>


    </ngx-datatable>

  </div>
</div>

<!-- Modal -->
<abp-modal [(visible)]="isModalOpen" [options]="{ size: 'xl' }">
  <ng-template #abpHeader>
    <h3>{{ (selectedRouterData?.id ? '::Permission:RouterDatas.Update' : '::Permission:RouterDatas.Create') | abpLocalization }}</h3>
  </ng-template>

    <ng-template #abpBody>
      <form [formGroup]="form" (ngSubmit)="save()">
        <div class="mt-2">
          <label for="routerData-code">{{'::RouterName' | abpLocalization}}</label><span> * </span>
          <input
            type="text"
            id="routerData-code"
            class="form-control"
            formControlName="routerName"
            autofocus
          />
        </div>

        <div class="mt-2">
          <label for="operation-status">{{'::RouterType' | abpLocalization}}</label><span> * </span>
          <select
            class="form-control"
            id="operation-status"
            formControlName="routerType"
          >
            <option
              *ngFor="let status of statusCreateOptions"
              [ngValue]="status"
            >
              {{ (status) | abpLocalization }}
            </option>
          </select>
        </div>

        <div class="mt-2">
          <label for="error-message">{{'::RouterContent' | abpLocalization}}</label><span> * </span>
          <textarea
            id="error-message"
            class="form-control"
            rows="9"
            formControlName="contentData"
          ></textarea>
        </div>
      </form>
    </ng-template>


  <ng-template #abpFooter>
    <button type="button" class="btn btn-secondary" abpClose>
      {{ '::Close' | abpLocalization }}
    </button>
    <button class="btn btn-primary" (click)="save()" [disabled]="form.invalid">
      <i class="fa fa-check mr-1"></i>
      {{ '::Save' | abpLocalization }}
    </button>
  </ng-template>
</abp-modal>
