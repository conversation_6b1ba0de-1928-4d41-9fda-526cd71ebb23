import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RouterDataComponent } from './router-data.component';
import { authGuard, permissionGuard } from '@abp/ng.core';

const routes: Routes = [{ path: '', component: RouterDataComponent, canActivate: [authGuard, permissionGuard]}];
// const routes: Routes = [{ path: '', component: RouterDataComponent}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RouterDataRoutingModule { }
