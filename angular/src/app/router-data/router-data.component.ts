import { ListService, PagedResultDto } from '@abp/ng.core';
import { Component, OnInit, ViewChild } from '@angular/core';
import { RouterDataDto, RouterDataService } from '../proxy/router-datas';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { NgbDateNativeAdapter, NgbDateAdapter } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationService, Confirmation } from '@abp/ng.theme.shared';
import { ToasterService } from '@abp/ng.theme.shared';
import { ContextMenuItemModel, ContextMenuService, Data, GridComponent, PageService, } from '@syncfusion/ej2-angular-grids';
import { TabComponent, } from '@syncfusion/ej2-angular-navigations';

@Component({
  selector: 'app-router-data',
  templateUrl: './router-data.component.html',
  styleUrls: ['./router-data.component.scss'],
  standalone: false,
  providers: [ListService],
})
export class RouterDataComponent implements OnInit {
  @ViewChild('treegrid')
  public treegrid?: GridComponent;
  routerData = { items: [], totalCount: 0 } as PagedResultDto<RouterDataDto>;
  selectedRouterData = {} as RouterDataDto;
  form: FormGroup;
  filterForm: FormGroup;
  //router-dataStatusOptions = router-dataStatusOptions;
  isModalOpen = false;
  statusOptions = ['',
  'PALLET', 
  'TROLLEY',
  'BOX'
  ];
    statusCreateOptions = [
  'PALLET', 
  'TROLLEY',
  'BOX'
  ];
  pageSettings: any = { pageSize: 30, pageSizes: [30, 50, 100, 200, 300] }; // Kích thước trang mặc định


  constructor(
    public readonly list: ListService,
    private fb: FormBuilder,
    private routerDataService: RouterDataService,
    private confirmation: ConfirmationService,
    private toaster: ToasterService
  ) {
    // Khởi tạo form lọc
    this.filterForm = this.fb.group({
      routerName: [''],
      routerType: [''],
      router: [''],
    });

    // Kết nối dữ liệu với bộ lọc
    this.list.hookToQuery((query) => this.getData(query)).subscribe((res) => {
      this.routerData = res;
    });
  }

  ngOnInit(): void {
    this.pageSettings = { pageSize: 30, pageCount: 0, currentPage: 1, totalRecordsCount: 0, };
  }

  getData(query) {
    const filter = this.filterForm.value;
    return this.routerDataService.getList({
      ...query,
      routerName: filter.routerName || undefined,
      routerType: filter.routerType || undefined,
      router: filter.router || undefined,
    });
    // // Cập nhật lại số trang trong grid
    //     this.pageSettings = {
    //       pageSize: this.searchModel.pageSize,
    //       pageSizes: [30, 50, 100, 200, 300],
    //       totalItems: this.totalItems,
    //       totalDataRecordsCount: this.totalItems,
    //       totalItemsCount: this.totalItems,
    //       enableQueryString: false,
    //       alwaysShow: false,
    //     };
    //   },
  }

  search() {
    this.list.get();
  }

  resetFilters() {
    this.filterForm.reset();
    this.list.get();
  }

  createRouterData() {
    this.selectedRouterData = {} as RouterDataDto;
    this.buildForm();
    this.isModalOpen = true;
  }

  editRouterData(id: string) {
    this.routerDataService.get(id).subscribe((routerData) => {
      this.selectedRouterData = routerData;
      this.buildForm();
      this.isModalOpen = true;
    });
  }

  buildForm() {
    this.form = this.fb.group({
      routerName: [this.selectedRouterData.routerName || '', Validators.required],
      routerType: [this.selectedRouterData.routerType || '', Validators.required],
      contentData: [this.selectedRouterData.contentData || '', Validators.required],
    });
  }

  save() {
    if (this.form.invalid) return;

    const request = this.selectedRouterData.id
      ? this.routerDataService.update(this.selectedRouterData.id, this.form.value)
      : this.routerDataService.create(this.form.value);

    request.subscribe(() => {
      this.isModalOpen = false;
      this.form.reset();
      this.list.get();
    });
  }

  deleteRouterData(id: string) {
    this.confirmation.warn('::AreYouSureToDelete', '::AreYouSure').subscribe((status) => {
      if (status === Confirmation.Status.confirm) {
        this.routerDataService.delete(id).subscribe(() => this.list.get());
      }
    });
  }

  selectedFile?: File;

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input?.files?.length) {
    this.selectedFile = input.files[0];
    }
  }

  onUpload() {
    if (!this.selectedFile) return;
    const formData = new FormData();
    formData.append('file', this.selectedFile);

    this.routerDataService.upload(formData).subscribe({
      next: () => {
        this.toaster.success('SOR::SaveSuccess', 'SOR::Success');
        this.list.get();
      },
      error: (err) => {
        this.toaster.error('SOR::SaveError', 'SOR::Error');
      }
    });
  }

    actionBegin(args: any): void {
    // if (args.requestType === 'sorting') {
    //   this.searchModel.orderBy = args.columnName;
    //   this.searchModel.orderType = args.direction;
    //   this.search();
    // } else if (args.requestType === 'paging') {
    //   this.searchModel.pageNumber = args.page;
    //   this.search();
    // } else if (args.requestType === 'pagesize') {
    //   this.searchModel.pageSize = args.pageSize;
    //   this.searchModel.pageNumber = 1; // Reset về trang 1
    //   this.search();
    // }
  }
}
