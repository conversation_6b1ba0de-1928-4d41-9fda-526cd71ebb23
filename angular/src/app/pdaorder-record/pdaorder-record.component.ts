import { ListService, PagedResultDto } from '@abp/ng.core';
import { Component, OnInit } from '@angular/core';
import { PDAOrderRecordDto, PDAOrderRecordService, RetryTaskService } from '../proxy/pdaorder-records';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { NgbDateNativeAdapter, NgbDateAdapter } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationService, Confirmation } from '@abp/ng.theme.shared';
import { DatePipe } from '@angular/common';
import { ToasterService } from '@abp/ng.theme.shared';

@Component({
  selector: 'app-pdaorder-record',
  templateUrl: './pdaorder-record.component.html',
  styleUrl: './pdaorder-record.component.scss',
  standalone: false,
  providers: [ListService, { provide: NgbDateAdapter, useClass: NgbDateNativeAdapter }],
})
export class PDAOrderRecordComponent implements OnInit {
  pdaOrderRecord = { items: [], totalCount: 0 } as PagedResultDto<PDAOrderRecordDto>;
  selectedPDAOrderRecord = {} as PDAOrderRecordDto; // declare selectedPDAOrderRecord
  form: FormGroup;
  filterForm: FormGroup;
  //pdaOrderRecordTypes = pdaOrderRecordTypeOptions;
  isModalOpen = false;
    statusOptions = ['',
  'PALLET', 
  'TROLLEY',
  'BOX'
  ];

  statusSendOptions = ['',
  '0', '1','99'];
  constructor(public readonly list: ListService, 
    private pdaOrderRecordService: PDAOrderRecordService, 
    private fb: FormBuilder,
    private confirmation: ConfirmationService, 
    private datePipe: DatePipe,
    private toaster: ToasterService,
    private retryService: RetryTaskService
  ) {

        const date = new Date();

        // Khởi tạo form lọc
    this.filterForm = this.fb.group({
      deviceCode: [''],
      agvType: [''],
      agvCode: [''],
      callSize: [''],
      status: [''],
      dateFrom: [date],
      dateTo: [date]
    });

        // Kết nối dữ liệu với bộ lọc
    this.list.hookToQuery((query) => this.getData(query)).subscribe((res) => {
      this.pdaOrderRecord = res;
    });
  }
  

  ngOnInit() {
    console.log('RouterDataComponent initialized');
  }

    getData(query) {
    const filter = this.filterForm.value;

    return this.pdaOrderRecordService.getList({
      ...query,
      deviceCode: filter.deviceCode || undefined,
      agvType: filter.agvType || undefined,
      callSize: filter.callSize || undefined,
      status: filter.status || undefined,
      agvCode: filter.agvCode || undefined,
      dateFrom: filter.dateFrom ? this.datePipe.transform(filter.dateFrom, 'yyyy-MM-dd') : null,
      dateTo: filter.dateTo ? this.datePipe.transform(filter.dateTo, 'yyyy-MM-dd') : null
    });
  }

  search() {
    this.list.get();
  }

  resetFilters() {
    this.filterForm.reset();
    this.list.get();
  }

  createPDAOrderRecord() {
    this.selectedPDAOrderRecord = {} as PDAOrderRecordDto; // reset the selected pdaOrderRecord
    this.buildForm();
    this.isModalOpen = true;
  }

  // Add editPDAOrderRecord method
  editPDAOrderRecord(id: string) {
    this.pdaOrderRecordService.get(id).subscribe((pdaOrderRecord) => {
      this.selectedPDAOrderRecord = pdaOrderRecord;
      this.buildForm();
      this.isModalOpen = true;
    });
  }

  buildForm() {
    this.form = this.fb.group({
      content: [{ value: this.selectedPDAOrderRecord.content || '', disabled: true }],
      sendContent: [{ value: this.selectedPDAOrderRecord.sendContent || '', disabled: true }],
      listPoint: [{ value: this.selectedPDAOrderRecord.listPoint || '', disabled: true }],
      agvType: [{ value: this.selectedPDAOrderRecord.agvType || '', disabled: true }],
      agvCode: [{ value: this.selectedPDAOrderRecord.agvCode || '', disabled: true }],
      deviceCode: [{ value: this.selectedPDAOrderRecord.deviceCode || '', disabled: true }],
      containerType: [{ value: this.selectedPDAOrderRecord.containerType || '', disabled: true }],
    });
  }
  // change the save method
  save() {
    if (this.form.invalid) {
      return;
    }

    const request = this.selectedPDAOrderRecord.id
      ? this.pdaOrderRecordService.update(this.selectedPDAOrderRecord.id, this.form.value)
      : this.pdaOrderRecordService.create(this.form.value);

    request.subscribe(() => {
      this.isModalOpen = false;
      this.form.reset();
      this.list.get();
    });
  }

  delete(id: string) {
    this.confirmation.warn('::AreYouSureToDelete', '::AreYouSure').subscribe((status) => {
    if (status === Confirmation.Status.confirm) {
      this.pdaOrderRecordService.delete(id).subscribe(() => this.list.get());
    }
  });
}

  getStatusClass(status: string): string {
    switch (status) {
      case '2':
        return 'badge bg-secondary'; // xám
      case '0':
        return 'badge bg-success'; // xanh lá
      case '99':
        return 'badge bg-danger'; // đỏ
      default:
        return 'badge bg-warning text-dark'; // mặc định
    }
  }

  fRetry(id: string): void {
    this.retryService.retryTask(id).subscribe(result => {
      if(result){
        this.toaster.success('SOR::ActionSuccess', 'SOR::Success');
          this.search();
      }else{
        this.toaster.error('SOR::ActionSuccess', 'SOR::Error');
      }
    });
  }
  
}
