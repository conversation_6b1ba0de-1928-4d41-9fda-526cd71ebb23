import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PDAOrderRecordComponent } from './pdaorder-record.component';
import { authGuard, permissionGuard } from '@abp/ng.core';

const routes: Routes = [{ path: '', component: PDAOrderRecordComponent, canActivate: [authGuard, permissionGuard]}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PDAOrderRecordRoutingModule { }
