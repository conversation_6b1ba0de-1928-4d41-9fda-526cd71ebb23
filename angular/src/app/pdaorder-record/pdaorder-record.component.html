


<div class="card">
  <div class="card-header">
    <div class="row">
      <div class="col col-md-6">
        <h5 class="card-title">
          {{ '::Menu:PDAOrderRecord' | abpLocalization }}
        </h5>
      </div>
      <div class="text-end col col-md-6"></div>
    </div>
  </div>
  <div class="card-body">
    <form [formGroup]="filterForm" (ngSubmit)="search()" class="mb-3 row align-items-end">

        <div class="col-md-3">
    <label>{{ '::AGVType' | abpLocalization }}</label>
    <select class="form-control" formControlName="agvType">
      <option *ngFor="let status of statusOptions" [ngValue]="status">
        {{ status === '' ? ('::All' | abpLocalization) : ('::Enum:Send.' + status) | abpLocalization }}
      </option>
    </select>
  </div>
  <div class="col-md-3">
    <label>{{ '::DeviceCode' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="deviceCode" />
  </div>

    <div class="col-md-3">
    <label>{{ '::AGVCode' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="agvCode" />
  </div>

      <div class="col-md-3">
    <label>{{ '::CallSize' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="callSize" />
  </div>

          <div class="col-md-3">
    <label>{{ '::Status' | abpLocalization }}</label>
    <select class="form-control" formControlName="status">
      <option *ngFor="let status of statusSendOptions" [ngValue]="status">
        {{ status === '' ? ('::All' | abpLocalization) : ('::Enum:Send.' + status) | abpLocalization }}
      </option>
    </select>
  </div>

  <div class="col-md-3">
    <label>{{ '::DateFrom' | abpLocalization }}</label>
    <input
        #datepicker1="ngbDatepicker"
        class="form-control"
        name="datepickerFrom"
        formControlName="dateFrom"
        ngbDatepicker
        (click)="datepicker1.toggle()"
      />
  </div>
    <div class="col-md-3">
    <label>{{ '::DateTo' | abpLocalization }}</label>
    <input
        #datepicker2="ngbDatepicker"
        class="form-control"
        name="datepickerTo"
        formControlName="dateTo"
        ngbDatepicker
        (click)="datepicker2.toggle()"
      />
  </div>
  <div class="col-md-2 text-end d-flex gap-2">
    <button class="btn btn-primary btn-sm w-auto" type="submit">
      <i class="fa fa-search me-1"></i>{{ '::Search' | abpLocalization }}
    </button>
    <button class="btn btn-secondary btn-sm w-auto" type="button" (click)="resetFilters()">
      <i class="fa fa-times me-1"></i>{{ '::Clear' | abpLocalization }}
    </button>
  </div>
</form>
    <ngx-datatable [rows]="pdaOrderRecord.items" [count]="pdaOrderRecord.totalCount" [list]="list" default  [columnMode]="'force'">
      <ngx-datatable-column [name]="'::Actions' | abpLocalization" [sortable]="false" [maxWidth]="150">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div ngbDropdown container="body" class="d-inline-block">
            <button class="btn btn-primary btn-sm dropdown-toggle" ngbDropdownToggle>
              <i class="fa fa-cog me-1"></i>{{ '::Actions' | abpLocalization }}
            </button>
            <div ngbDropdownMenu>
              <button *ngIf="row.status === '1'" ngbDropdownItem (click)="fRetry(row.id)">
                {{ '::Retry' | abpLocalization }}
              </button>
              <button ngbDropdownItem (click)="editPDAOrderRecord(row.id)">
                {{ '::Detail' | abpLocalization }}
              </button>
            </div>
          </div>
        </ng-template>
    </ngx-datatable-column>
      <ngx-datatable-column [name]="'::AGVType' | abpLocalization" [flexGrow]="1" prop="agvType"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::DeviceCode' | abpLocalization" [flexGrow]="1" prop="deviceCode"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::AGVCode' | abpLocalization" [flexGrow]="1" prop="agvCode"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::CallSize' | abpLocalization" [flexGrow]="1" prop="callSize"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::Status' | abpLocalization" [flexGrow]="1" prop="status">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span [ngClass]="getStatusClass(row.status)">           
            {{ ('::Enum:Send.' + row.status) | abpLocalization }}
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::CapturedAt' | abpLocalization" [flexGrow]="1" prop="creationTime">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.creationTime | date: 'dd/MM/yyyy HH:mm:ss' }}
        </ng-template>
      </ngx-datatable-column>

    </ngx-datatable>
  </div>
</div>

<!-- Modal -->
<abp-modal [(visible)]="isModalOpen" [options]="{ size: 'xl' }">
  <ng-template #abpHeader>
    <h3>{{ ('::PDADetail') | abpLocalization }}</h3>
  </ng-template>

    <ng-template #abpBody>
      <form [formGroup]="form" (ngSubmit)="save()">
        <div class="mt-2">
          <label for="agv-type">{{ '::AGVType' | abpLocalization }}</label>
          <input
            type="text"
            id="agv-type"
            class="form-control"
            formControlName="agvType"
          />
        </div>

        <div class="mt-2">
          <label for="device-code">{{'::DeviceCode' | abpLocalization}}</label>
          <input
            type="text"
            id="device-code"
            class="form-control"
            formControlName="deviceCode"
          />
        </div>

        <div class="mt-2">
          <label for="agv-code">{{'::AGVCode' | abpLocalization}}</label>
          <input
            type="text"
            id="agv-code"
            class="form-control"
            formControlName="agvCode"
          />
        </div>

        <div class="mt-2">
          <label for="container-type">{{ '::ContainerType' | abpLocalization }}</label>
          <input
            type="text"
            id="container-type"
            class="form-control"
            formControlName="containerType"
          />
        </div>

        <div class="mt-2">
          <label for="list-point">{{'::ListPoint' | abpLocalization}}</label>
          <input
            type="text"
            id="list-point"
            class="form-control"
            formControlName="listPoint"
          />
        </div>

        <div class="mt-2">
          <label for="content">{{'::Content' | abpLocalization}}</label>
          <textarea
            id="content"
            class="form-control"
            rows="5"
            formControlName="content"
          ></textarea>
        </div>

        <div class="mt-2">
          <label for="send-content">{{'::SendContent' | abpLocalization}}</label>
          <textarea
            id="send-content"
            class="form-control"
            rows="5"
            formControlName="sendContent"
          ></textarea>
        </div>
      </form>
    </ng-template>


  <ng-template #abpFooter>
    <button type="button" class="btn btn-secondary" abpClose>
      {{ '::Close' | abpLocalization }}
    </button>
  </ng-template>
</abp-modal>
