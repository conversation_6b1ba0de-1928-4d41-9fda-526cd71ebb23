import { ListService, PagedResultDto } from '@abp/ng.core';
import { Component, OnInit, ViewChild } from '@angular/core';
import { StatisticDto, StatisticService, StatisticDetail } from '../proxy/statistic';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { NgbDateNativeAdapter, NgbDateAdapter } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationService, Confirmation } from '@abp/ng.theme.shared';
import { ChartConfiguration, ChartType } from 'chart.js';
import { MaterialOrderDto } from '../proxy/material-orders';
import { AGVTaskDto } from '../proxy/agvtasks';
import { BaseChartDirective } from 'ng2-charts';
import { NgZone } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { LocalizationService } from '@abp/ng.core';

@Component({
  selector: 'app-statistic',
  templateUrl: './statistic.component.html',
  styleUrl: './statistic.component.scss',
  standalone: false,
  providers: [ListService],
})
export class StatisticComponent implements OnInit {
  statistic = {} as StatisticDto; // declare selectedStatistic
  form: FormGroup;

  @ViewChild('agvBarChart', { static: false, read: BaseChartDirective }) agvChart?: BaseChartDirective;
  @ViewChild('orderBarChart', { static: false, read: BaseChartDirective }) orderChart?: BaseChartDirective;


    // Biểu đồ hình tròn
  pieChartLabels: string[] = ['AGV Task', 'Terminal Task', 'Unrealized'];
  pieChartData: ChartConfiguration<'pie'>['data'] = {
  labels: this.pieChartLabels,
  datasets: [{ data: [] }]
};


  // Biểu đồ cột
  barChartLabels: string[] = ['0-10p', '10-30p', '30-60p', '60-120p', '120p++'];
  agvBarChartData: ChartConfiguration<'bar'>['data'] = {
    labels: this.barChartLabels,
    datasets: [{ data: [], label: 'AGV Tasks' }]
  };
  orderBarChartData: ChartConfiguration<'bar'>['data'] = {
    labels: this.barChartLabels,
    datasets: [{ data: [], label: 'Material Orders' }]
  };
  
  isModalOpen = false;
  constructor(public readonly list: ListService, 
    private statisticService: StatisticService, 
    private ngZone: NgZone,
    private router: Router,
    private l: LocalizationService,
  ) {
    router.events
    .pipe(filter(event => event instanceof NavigationEnd))
    .subscribe(() => {
      this.getData();
    });
  }

  ngOnInit() {
    this.getData();
  }

  getData(afterLoad?: () => void) {

    this.statisticService.getStatistic().subscribe((data) => {
      this.statistic = data;

          // Bar Chart: AGV Task theo TimeType
      const agvTimeCount = this.mapToTimeTypeArray(this.statistic.agvTask ?? []);
      const orderTimeCount = this.mapToTimeTypeArray(this.statistic.materialOrder ?? []);

          // 🔄 Tạo lại object để Angular và Chart.js nhận biết thay đổi
    this.pieChartData = {
  labels: [
    this.l.instant('::Chart:AGVTask'),
    this.l.instant('::Chart:TerminalTask'),
    this.l.instant('::Chart:Unrealized')
  ],
  datasets: [{
    data: [
      this.statistic.toltalOrderAgv,
      this.statistic.toltalOrderTerminal,
      this.statistic.toltalOrder - this.statistic.toltalOrderAgv - this.statistic.toltalOrderTerminal
    ]
  }]
};

this.agvBarChartData = {
  labels: [
    this.l.instant('::Chart:Time0_10'),
    this.l.instant('::Chart:Time10_30'),
    this.l.instant('::Chart:Time30_60'),
    this.l.instant('::Chart:Time60_120'),
    this.l.instant('::Chart:Time120Plus')
  ],
  datasets: [{
    data: [...agvTimeCount],
    label: this.l.instant('::Chart:AGVTask')
  }]
};

this.orderBarChartData = {
  labels: [
    this.l.instant('::Chart:Time0_10'),
    this.l.instant('::Chart:Time10_30'),
    this.l.instant('::Chart:Time30_60'),
    this.l.instant('::Chart:Time60_120'),
    this.l.instant('::Chart:Time120Plus')
  ],
  datasets: [{
    data: [...orderTimeCount],
    label: this.l.instant('::Chart:MaterialOrders') // Thêm khóa này nếu cần
  }]
};

    // Gọi callback sau khi load xong dữ liệu
    if (afterLoad) afterLoad();

    this.ngZone.runOutsideAngular(() => {
      setTimeout(() => {
        this.agvChart?.update();
        this.orderChart?.update();
      });
    });

    });



  }

  // Chuyển danh sách StatisticDetail thành mảng 5 phần tử theo TimeType
  mapToTimeTypeArray(details: StatisticDetail[] | undefined): number[] {
    const counts = [0, 0, 0, 0, 0];
    if (!Array.isArray(details)) return counts;

    for (const detail of details) {
      const index = detail.timeType - 1;
      if (index >= 0 && index < 5 && Array.isArray(detail.id)) {
        counts[index] = detail.id.length;
      }
    }

    return counts;
  }

    // Thêm mảng để hiển thị kết quả
  selectedChartSource: 'agv' | 'order' | null = null;
  selectedTaskDetails: AGVTaskDto[] | MaterialOrderDto[] = [];
  selectedTimeType: number | null = null;


  onChartClick(event: any, source: 'agv' | 'order') {
    this.selectOrderDetail = [];
  const activePoints = event?.active;
  const chart = event?.chart;

  if (!activePoints?.length) return;

  const point = activePoints[0];
  const index = point.index;
  const timeType = index + 1;

  const detailList = source === 'agv' ? this.statistic.agvTask : this.statistic.materialOrder;
  const selected = detailList.find(x => x.timeType === timeType);
  if (!selected || selected.id.length === 0) {
    this.selectedTaskDetails = [];
    return;
  }

  this.selectedChartSource = source;
  this.selectedTimeType = timeType; // <-- thêm dòng này

  if (source === 'agv') {
    this.statisticService.getStatisticTask(selected.id).subscribe(result => {
      this.selectedTaskDetails = result;
    });
  } else {
    this.statisticService.getStatisticOrder(selected.id).subscribe(result => {
      this.selectedTaskDetails = result;
    });
  }
}

getTaskStatusClass(status: string): string {
  switch (status) {
    case 'Created':
    case '1':
      return 'badge bg-secondary'; // xám
    // case 'Executing':
    // case '2':
    //   return 'badge bg-warning text-dark'; // vàng
    // case 'Sending':
    // case '3':
    //   return 'badge bg-primary'; // xanh dương
    case 'Success':
    case '9':
      return 'badge bg-success'; // xanh lá
    case '0':
    case '4':
    case '5':
      return 'badge bg-danger'; // đỏ
    default:
      return 'badge bg-warning text-dark'; // mặc định
  }
}

  getOrderStatusClass(status: string): string {
  switch (status) {
    case 'Created':
    case '2':
      return 'badge bg-secondary'; // xám
    // case 'Executing':
    // case '2':
    //   return 'badge bg-warning text-dark'; // vàng
    // case 'Sending':
    // case '3':
    //   return 'badge bg-primary'; // xanh dương
    case 'Success':
    case '1':
      return 'badge bg-success'; // xanh lá
    case '0':
    case '4':
    case '5':
      return 'badge bg-danger'; // đỏ
    default:
      return 'badge bg-warning text-dark'; // mặc định
  }
}

handleAction(id: string) {
  this.statisticService.updateOrder(id).subscribe({
    next: () => {
      // Cập nhật biểu đồ (gọi lại dữ liệu tổng thể)
      this.getData(() => {
        // Cập nhật biểu đồ chi tiết (sau khi biểu đồ chính đã update)
        if (this.selectedChartSource && this.selectedTimeType) {
          const fakeChartEvent = {
            active: [{ index: this.selectedTimeType - 1 }],
            chart: null
          };
          this.onChartClick(fakeChartEvent, this.selectedChartSource);
        }
        if (this.selectedChartSource === 'agv') {
          this.selectedTaskDetails = (this.selectedTaskDetails as AGVTaskDto[]).filter(item => item.id !== id);
        } else {
          this.selectedTaskDetails = (this.selectedTaskDetails as MaterialOrderDto[]).filter(item => item.id !== id);
        }
      });


    }
  });
}

selectOrderDetail: MaterialOrderDto[] = [];

onPieChartClick(event: { event?: MouseEvent; active?: any[] }) {
  this.selectedTaskDetails = [];
  const activePoint = event?.active?.[0];
  if (!activePoint) return;

  const index = activePoint.index;

  // Dùng index để nhận diện loại
  const category =
    index === 0 ? '0'
    : index === 1 ? '1'
    : null;

  this.statisticService.getStatisticRatio(category).subscribe(result => {
      this.selectOrderDetail = result;
    });
}




}
