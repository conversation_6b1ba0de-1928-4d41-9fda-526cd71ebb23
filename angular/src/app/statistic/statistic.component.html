

<div class="card">
  <div class="card-body">
    <div class="row">
  <!-- Bi<PERSON><PERSON> đồ hình tròn -->
      <div class="col-md-4">
        <h5 class="text-center">{{'::Chart:Ratio' | abpLocalization}}</h5>
        <div class="chart-container">
          <canvas baseChart
                  [data]="pieChartData"
                  [labels]="pieChartLabels"
                  [type]="'pie'"
                  (chartClick)="onPieChartClick($event)">
          </canvas>
        </div>
      </div>

      <!-- <PERSON><PERSON><PERSON><PERSON> đồ cột AGV Task -->
      <div class="col-md-4">
        <h5 class="text-center">{{'::Chart:AGVTime' | abpLocalization}}</h5>
        <div class="chart-container" *ngIf="agvBarChartData.datasets[0].data.length">
          <canvas baseChart
                  #agvBarChart
                  [data]="agvBarChartData"
                  [labels]="barChartLabels"
                  [type]="'bar'"
                  (chartClick)="onChartClick($event, 'agv')">
          </canvas>
        </div>
      </div>

      <!-- Biểu đồ cột Material Order -->
      <div class="col-md-4">
        <h5 class="text-center">{{'::Chart:OrderTime' | abpLocalization}}</h5>
        <div class="chart-container" *ngIf="orderBarChartData.datasets[0].data.length">
          <canvas baseChart
                  #orderBarChart
                  [data]="orderBarChartData"
                  [labels]="barChartLabels"
                  [type]="'bar'"
                  (chartClick)="onChartClick($event, 'order')">
          </canvas>
        </div>
      </div>
</div>


    <!-- Modal -->
    <div class="mt-4" *ngIf="selectedTaskDetails.length > 0">
      <h5>
        {{ (selectedChartSource === 'agv' ? '::AGVTaskDetail' : '::MaterialOrderDetail') | abpLocalization }}
      </h5>

      <div *ngIf="selectedChartSource === 'agv'">
        <ngx-datatable [rows]="selectedTaskDetails" [count]="selectedTaskDetails.length" default [columnMode]="'force'">
      <ngx-datatable-column [name]="'::PDAOrderRecordID' | abpLocalization" [flexGrow]="1" prop="pdaOrderRecordID"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::AGVType' | abpLocalization" [flexGrow]="1" prop="taskType"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::AGVID' | abpLocalization" [flexGrow]="1" prop="agvid"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::Router' | abpLocalization" [flexGrow]="1" prop="router"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::PointOfFeeding' | abpLocalization" [flexGrow]="1" prop="targetRoute"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::Status' | abpLocalization" [flexGrow]="1" prop="taskStatus">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span [ngClass]="getTaskStatusClass(row.taskStatus)">
            {{ ('::Enum:AgvTaskStatus.' + row.taskStatus) | abpLocalization }}
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::TaskCreatedAt' | abpLocalization" [flexGrow]="1" prop="taskCreatedAt">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.taskCreatedAt | date: 'dd/MM/yyyy HH:mm:ss' }}
        </ng-template>
      </ngx-datatable-column>
        </ngx-datatable>
      </div>

      <div *ngIf="selectedChartSource === 'order'">
        <ngx-datatable [rows]="selectedTaskDetails" [count]="selectedTaskDetails.length" default [columnMode]="'force'">
                <ngx-datatable-column [name]="'::OrderID' | abpLocalization" [flexGrow]="1" prop="orderID"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::PartNumber' | abpLocalization" [flexGrow]="1" prop="partNumber"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::BatchNumber' | abpLocalization" [flexGrow]="1" prop="batchNumber"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::Quantity' | abpLocalization" [flexGrow]="1" prop="quantity"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::CapturedAt' | abpLocalization" [flexGrow]="1" prop="creationTime">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.creationTime | date: 'dd/MM/yyyy HH:mm:ss' }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::Status' | abpLocalization" [flexGrow]="1" prop="status">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span [ngClass]="getTaskStatusClass(row.status)">
            {{ ('::Enum:OrderStatus.' + row.status) | abpLocalization }}
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::Actions' | abpLocalization" [sortable]="false" [maxWidth]="150">
      <ng-template let-row="row" ngx-datatable-cell-template>
        <button class="btn btn-sm btn-primary" (click)="handleAction(row.id)">
          <i class="fas fa-check"></i> {{ '::Complate' | abpLocalization }}
        </button>
      </ng-template>
    </ngx-datatable-column>
        </ngx-datatable>
    </div>
    </div>


        <div class="mt-4" *ngIf="selectOrderDetail.length > 0">
      <h5>
        {{ '::ListOrderDetail' | abpLocalization }}
      </h5>

        <ngx-datatable [rows]="selectOrderDetail" [count]="selectOrderDetail.length" default [columnMode]="'force'">
                <ngx-datatable-column [name]="'::OrderID' | abpLocalization" [flexGrow]="1" prop="orderID"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::PartNumber' | abpLocalization" [flexGrow]="1" prop="partNumber"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::BatchNumber' | abpLocalization" [flexGrow]="1" prop="batchNumber"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::Quantity' | abpLocalization" [flexGrow]="1" prop="quantity"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::CapturedAt' | abpLocalization" [flexGrow]="1" prop="creationTime">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.creationTime | date: 'dd/MM/yyyy HH:mm:ss' }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::Status' | abpLocalization" [flexGrow]="1" prop="status">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span [ngClass]="getOrderStatusClass(row.status)">
            {{ ('::Enum:OrderStatus.' + row.status) | abpLocalization }}
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::DateComplated' | abpLocalization" [flexGrow]="1" prop="timeComplated">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.timeComplated | date: 'dd/MM/yyyy HH:mm:ss' }}
        </ng-template>
      </ngx-datatable-column>
        </ngx-datatable>
    </div>
  </div>
</div>



