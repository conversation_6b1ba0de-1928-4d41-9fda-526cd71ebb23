

<div class="card">
  <div class="card-header router-header">
    <div class="row">
      <div class="col col-md-6">
        <h5 class="card-title">{{ '::Menu:Flag' | abpLocalization }}</h5>
      </div>
      <div class="col-md-6 text-end d-flex justify-content-end gap-2 flex-wrap pt-2">
        <button *abpPermission="'SOR.Flags.Create'" id="create" class="btn btn-primary" type="button" (click)="createFlag()">
          <i class="fa fa-plus me-1"></i>
          <span>{{ "::Create" | abpLocalization }}</span>
        </button>

        <a class="btn btn-outline-secondary btn-sm d-flex align-items-center" (click)="downloadSample()">
          <i class="fa fa-download me-1"></i> Tải mẫu
        </a>

        <form (ngSubmit)="onUpload()" #uploadForm="ngForm" class="d-flex align-items-center gap-2 upload-form">
          <input type="file" class="form-control form-control-sm" (change)="onFileSelected($event)" accept=".xlsx" required />
          <button class="btn btn-secondary btn-sm" type="submit" [disabled]="!selectedFile">
            <i class="fa fa-upload me-1"></i> Import
          </button>
        </form>
      </div>
    </div>
  </div>

  <div class="card-body">
    
<form [formGroup]="filterForm" (ngSubmit)="search()" class="mb-3 row align-items-end">
  <div class="col-md-3">
    <label>{{ '::FlagName' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="flagName" />
  </div>

  <div class="col-md-7"></div>
  <div class="col-md-2 text-end d-flex gap-2">
    <button class="btn btn-primary btn-sm w-auto" type="submit">
      <i class="fa fa-search me-1"></i>{{ '::Search' | abpLocalization }}
    </button>
    <button class="btn btn-secondary btn-sm w-auto" type="button" (click)="resetFilters()">
      <i class="fa fa-times me-1"></i>{{ '::Clear' | abpLocalization }}
    </button>
  </div>
</form>
    <ngx-datatable
      [rows]="flag.items"
      [count]="flag.totalCount"
      [list]="list" default
       [columnMode]="'force'">

      <!-- Actions Column -->
      <ngx-datatable-column
        [name]="'::Actions' | abpLocalization"
        [maxWidth]="150"
        [sortable]="false"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div ngbDropdown container="body" class="d-inline-block">
            <button class="btn btn-primary btn-sm dropdown-toggle" ngbDropdownToggle>
              <i class="fa fa-cog me-1"></i>{{ '::Actions' | abpLocalization }}
            </button>
            <div ngbDropdownMenu>
              <button *abpPermission="'SOR.Flags.Update'" ngbDropdownItem (click)="editFlag(row.id)">
                {{ '::Edit' | abpLocalization }}
              </button>
              <button *abpPermission="'SOR.Flags.Delete'" ngbDropdownItem (click)="deleteFlag(row.id)">
                {{ '::Delete' | abpLocalization }}
              </button>
            </div>
          </div>
        </ng-template>
      </ngx-datatable-column>

      <ngx-datatable-column [name]="'::FlagName' | abpLocalization" [flexGrow]="1" prop="flagName"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::X' | abpLocalization" [flexGrow]="1" prop="x">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.x }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::Y' | abpLocalization" [flexGrow]="1" prop="y">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.y  }}
        </ng-template>
      </ngx-datatable-column>
    </ngx-datatable>

  </div>
</div>

<!-- Modal -->
<abp-modal [(visible)]="isModalOpen">
  <ng-template #abpHeader>
    <h3>{{ (selectedFlag?.id ? '::Permission:Flags.Update' : '::Permission:Flags.Create') | abpLocalization }}</h3>
  </ng-template>

    <ng-template #abpBody>
      <form [formGroup]="form" (ngSubmit)="save()">
        <div class="mt-2">
          <label for="flag-name">{{'::FlagName' | abpLocalization}}</label><span> * </span>
          <input
            type="text"
            id="flag-name"
            class="form-control"
            formControlName="flagName"
            autofocus
          />
        </div>

        <div class="mt-2">
          <label for="x">{{'::X' | abpLocalization}}</label><span> * </span>
          <input
            type="number"
            id="x"
            class="form-control"
            formControlName="x"
          />
        </div>

                <div class="mt-2">
          <label for="y">{{'::Y' | abpLocalization}}</label><span> * </span>
          <input
            type="number"
            id="y"
            class="form-control"
            formControlName="y"
          />
        </div>
        
      </form>
    </ng-template>


  <ng-template #abpFooter>
    <button type="button" class="btn btn-secondary" abpClose>
      {{ '::Close' | abpLocalization }}
    </button>
    <button class="btn btn-primary" (click)="save()" [disabled]="form.invalid">
      <i class="fa fa-check mr-1"></i>
      {{ '::Save' | abpLocalization }}
    </button>
  </ng-template>
</abp-modal>