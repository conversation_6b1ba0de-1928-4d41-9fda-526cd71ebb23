import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FlagComponent } from './flag.component';
import { authGuard, permissionGuard } from '@abp/ng.core';

const routes: Routes = [{ path: '', component: FlagComponent, canActivate: [authGuard, permissionGuard]}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class FlagRoutingModule { }
