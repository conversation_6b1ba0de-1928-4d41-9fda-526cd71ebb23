import { ListService, PagedResultDto } from '@abp/ng.core';
import { Component, OnInit } from '@angular/core';
import { FlagDto, FlagService } from '../proxy/flags';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { NgbDateNativeAdapter, NgbDateAdapter } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationService, Confirmation } from '@abp/ng.theme.shared';
import { ToasterService } from '@abp/ng.theme.shared';

@Component({
  selector: 'app-flag',
  templateUrl: './flag.component.html',
  styleUrl: './flag.component.scss',
  standalone: false,
  providers: [ListService],
})
export class FlagComponent implements OnInit {
  flag = { items: [], totalCount: 0 } as PagedResultDto<FlagDto>;
  selectedFlag = {} as FlagDto; // declare selectedFlag
  form: FormGroup;
    filterForm: FormGroup;
  //flagTypes = flagTypeOptions;
  isModalOpen = false;
  constructor(public readonly list: ListService, 
    private flagService: FlagService, 
    private fb: FormBuilder,
    private confirmation: ConfirmationService,
    private toaster: ToasterService
  ) {

    // Khởi tạo form lọc
    this.filterForm = this.fb.group({
      flagName: [''],
    });

    // Kết nối dữ liệu với bộ lọc
    this.list.hookToQuery((query) => this.getData(query)).subscribe((res) => {
      this.flag = res;
    });
  }

  ngOnInit() {
    console.log('RouterDataComponent initialized');
  }

  getData(query) {
    const filter = this.filterForm.value;

    return this.flagService.getList({
      ...query,
      flagName: filter.flagName || undefined,
    });
  }

  search() {
    this.list.get();
  }

  resetFilters() {
    this.filterForm.reset();
    this.list.get();
  }

  createFlag() {
    this.selectedFlag = {} as FlagDto; // reset the selected flag
    this.buildForm();
    this.isModalOpen = true;
  }

  // Add editFlag method
  editFlag(id: string) {
    this.flagService.get(id).subscribe((flag) => {
      this.selectedFlag = flag;
      this.buildForm();
      this.isModalOpen = true;
    });
  }

  buildForm() {
    this.form = this.fb.group({
      flagName: [this.selectedFlag.flagName || '', Validators.required],
      x: [this.selectedFlag.x || '', Validators.required],
      y: [this.selectedFlag.y || '', Validators.required],
    });
  }

  // change the save method
  save() {
    if (this.form.invalid) {
      return;
    }

    const request = this.selectedFlag.id
      ? this.flagService.update(this.selectedFlag.id, this.form.value)
      : this.flagService.create(this.form.value);

    request.subscribe(() => {
      this.isModalOpen = false;
      this.form.reset();
      this.list.get();
    });
  }

  deleteFlag(id: string) {
    this.confirmation.warn('::AreYouSureToDelete', '::AreYouSure').subscribe((status) => {
    if (status === Confirmation.Status.confirm) {
      this.flagService.delete(id).subscribe(() => this.list.get());
    }
  });
}

selectedFile?: File;

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input?.files?.length) {
    this.selectedFile = input.files[0];
    }
  }

  onUpload() {
    if (!this.selectedFile) return;
    const formData = new FormData();
    formData.append('file', this.selectedFile);

    this.flagService.upload(formData).subscribe({
      next: () => {
        this.toaster.success('SOR::SaveSuccess', 'SOR::Success');
        this.list.get();
      },
      error: (err) => {
        this.toaster.error('SOR::SaveError', 'SOR::Error');
      }
    });
  }

  downloadSample() {
  this.flagService.download().subscribe({
    next: (blob) => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'sample-flag.xlsx';
      a.click();
      window.URL.revokeObjectURL(url);
    },
    error: (err) => {
      console.error('Lỗi khi tải file mẫu:', err);
    }
  });
}
}
