import type { StatisticDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { AGVTaskDto } from '../agvtasks/models';
import type { MaterialOrderDto } from '../material-orders/models';

@Injectable({
  providedIn: 'root',
})
export class StatisticService {
  apiName = 'Default';
  

  getStatistic = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, StatisticDto>({
      method: 'GET',
      url: '/api/app/statistic',
    },
    { apiName: this.apiName,...config });
  

  getStatisticOrder = (listId: string[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, MaterialOrderDto[]>({
      method: 'GET',
      url: '/api/app/statistic/order',
      params: { listId },
    },
    { apiName: this.apiName,...config });
  

  getStatisticRatio = (type: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, MaterialOrderDto[]>({
      method: 'GET',
      url: '/api/app/statistic/ratio',
      params: { type },
    },
    { apiName: this.apiName,...config });
  

  getStatisticTask = (listId: string[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, AGVTaskDto[]>({
      method: 'GET',
      url: '/api/app/statistic/task',
      params: { listId },
    },
    { apiName: this.apiName,...config });
  

  updateOrder = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/statistic/order-status',
      params: { id },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
