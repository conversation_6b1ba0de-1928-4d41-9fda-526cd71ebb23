import type { AuditedEntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import type { AuditedAggregateRoot } from '../volo/abp/domain/entities/auditing/models';

export interface CreateUpdateMaterialMasterDto {
  partNumber: string;
  partName?: string;
  description?: string;
  defaultPOF?: string;
}

export interface GetMaterialMasterInputDto extends PagedAndSortedResultRequestDto {
  partNumber?: string;
  defaultPOF?: string;
}

export interface MaterialMaster extends AuditedAggregateRoot<string> {
  partNumber?: string;
  partName?: string;
  description?: string;
  defaultPOF?: string;
}

export interface MaterialMasterDto extends AuditedEntityDto<string> {
  partNumber?: string;
  partName?: string;
  description?: string;
  defaultPOF?: string;
}
