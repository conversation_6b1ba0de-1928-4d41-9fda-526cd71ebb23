import type { MaterialMaster, MaterialMasterDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AddMaterialMasterService {
  apiName = 'Default';
  

  createUpdateMaterialByInput = (input: MaterialMaster, config?: Partial<Rest.Config>) =>
    this.restService.request<any, MaterialMasterDto>({
      method: 'POST',
      url: '/api/material-master/create-update',
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
