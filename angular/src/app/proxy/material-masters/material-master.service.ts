import type { CreateUpdateMaterialMasterDto, GetMaterialMasterInputDto, MaterialMasterDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class MaterialMasterService {
  apiName = 'Default';
  

  create = (model: CreateUpdateMaterialMasterDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, MaterialMasterDto>({
      method: 'POST',
      url: '/api/app/material-master',
      body: model,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/material-master/${id}`,
    },
    { apiName: this.apiName,...config });
  

  download = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, Blob>({
      method: 'POST',
      responseType: 'blob',
      url: '/api/app/material-master/download',
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, MaterialMasterDto>({
      method: 'GET',
      url: `/api/app/material-master/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: GetMaterialMasterInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<MaterialMasterDto>>({
      method: 'GET',
      url: '/api/app/material-master',
      params: { partNumber: input.partNumber, defaultPOF: input.defaultPOF, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, model: CreateUpdateMaterialMasterDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, MaterialMasterDto>({
      method: 'PUT',
      url: `/api/app/material-master/${id}`,
      body: model,
    },
    { apiName: this.apiName,...config });
  

  upload = (file: FormData, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/material-master/upload',
      body: file,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
