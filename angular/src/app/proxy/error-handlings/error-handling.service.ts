import type { CreateUpdateErrorHandlingDto, ErrorHandlingDto, GetErrorHandlingInputDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ErrorHandlingService {
  apiName = 'Default';
  

  create = (input: CreateUpdateErrorHandlingDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ErrorHandlingDto>({
      method: 'POST',
      url: '/api/app/error-handling',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/error-handling/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ErrorHandlingDto>({
      method: 'GET',
      url: `/api/app/error-handling/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: GetErrorHandlingInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ErrorHandlingDto>>({
      method: 'GET',
      url: '/api/app/error-handling',
      params: { agvCode: input.agvCode, errorCode: input.errorCode, dateFrom: input.dateFrom, dateTo: input.dateTo, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, input: CreateUpdateErrorHandlingDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ErrorHandlingDto>({
      method: 'PUT',
      url: `/api/app/error-handling/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
