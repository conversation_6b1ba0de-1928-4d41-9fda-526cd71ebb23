import type { AuditedEntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';

export interface CreateUpdateErrorHandlingDto {
  agvCode?: string;
  errorCode?: string;
  errorAt?: string;
  fixStatus?: string;
  fixDescription?: string;
  fixedAt?: string;
  fixedBy?: string;
}

export interface ErrorHandlingDto extends AuditedEntityDto<string> {
  agvCode?: string;
  errorCode?: string;
  errorAt?: string;
  fixStatus?: string;
  fixDescription?: string;
  fixedAt?: string;
  fixedBy?: string;
}

export interface GetErrorHandlingInputDto extends PagedAndSortedResultRequestDto {
  agvCode?: string;
  errorCode?: string;
  dateFrom?: string;
  dateTo?: string;
}
