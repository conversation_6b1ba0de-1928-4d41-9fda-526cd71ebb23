import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { WarnCallbackRequestDto, WarnCallbackResponse } from '../hik/models';

@Injectable({
  providedIn: 'root',
})
export class WarnCallbackService {
  apiName = 'Default';
  

  receiveWarnCallbackByRequest = (request: WarnCallbackRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, WarnCallbackResponse>({
      method: 'POST',
      url: '/service/rest/agvCallbackService/warnCallback',
      body: request,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
