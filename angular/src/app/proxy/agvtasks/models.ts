import type { AuditedEntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';

export interface AGVTaskDto extends AuditedEntityDto<string> {
  pdaOrderRecordID?: string;
  taskType?: string;
  targetRoute?: string;
  initPriotiry: number;
  robotTaskCode?: string;
  agvid?: string;
  taskStatus?: string;
  agvStatus?: string;
  taskCreatedAt?: string;
  taskCompletedAt?: string;
  taskID?: string;
  retry: number;
  router?: string;
  timeComplete: number;
}

export interface CreateUpdateAGVTaskDto {
  pdaOrderRecordID: string;
  taskType?: string;
  targetRoute?: string;
  initPriotiry: number;
  robotTaskCode?: string;
  agvid?: string;
  taskStatus?: string;
  agvStatus?: string;
  taskCreatedAt?: string;
  taskCompletedAt?: string;
  taskID?: string;
  retry: number;
  router?: string;
  timeComplete: number;
}

export interface GetAGVTaskInputDto extends PagedAndSortedResultRequestDto {
  taskType?: string;
  robotTaskCode?: string;
  taskStatus?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface ViewTaskCache {
  taskId?: string;
  routerName?: string;
  type?: string;
  agvCode?: string;
  agvStatus?: string;
  taskStatus?: string;
  retry?: number;
  time?: number;
  createAt?: string;
}
