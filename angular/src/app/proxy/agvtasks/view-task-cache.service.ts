import type { ViewTaskCache } from './models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ViewTaskCacheService {
  apiName = 'Default';
  

  getTask = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, ViewTaskCache[]>({
      method: 'GET',
      url: '/api/app/cache/get-task',
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
