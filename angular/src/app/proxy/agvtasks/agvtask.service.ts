import type { AGVTaskDto, CreateUpdateAGVTaskDto, GetAGVTaskInputDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AGVTaskService {
  apiName = 'Default';
  

  create = (input: CreateUpdateAGVTaskDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AGVTaskDto>({
      method: 'POST',
      url: '/api/app/a-gVTask',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/a-gVTask/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AGVTaskDto>({
      method: 'GET',
      url: `/api/app/a-gVTask/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: GetAGVTaskInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<AGVTaskDto>>({
      method: 'GET',
      url: '/api/app/a-gVTask',
      params: { taskType: input.taskType, robotTaskCode: input.robotTaskCode, taskStatus: input.taskStatus, dateFrom: input.dateFrom, dateTo: input.dateTo, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, input: CreateUpdateAGVTaskDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AGVTaskDto>({
      method: 'PUT',
      url: `/api/app/a-gVTask/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
