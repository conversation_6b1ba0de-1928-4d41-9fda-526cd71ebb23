import type { AGVDto, CreateUpdateAGVDto, GetAGVInputDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AGVService {
  apiName = 'Default';
  

  create = (model: CreateUpdateAGVDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AGVDto>({
      method: 'POST',
      url: '/api/app/a-gV',
      body: model,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/a-gV/${id}`,
    },
    { apiName: this.apiName,...config });
  

  download = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, Blob>({
      method: 'POST',
      responseType: 'blob',
      url: '/api/app/a-gV/download',
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AGVDto>({
      method: 'GET',
      url: `/api/app/a-gV/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: GetAGVInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<AGVDto>>({
      method: 'GET',
      url: '/api/app/a-gV',
      params: { agvCode: input.agvCode, operationStatus: input.operationStatus, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, model: CreateUpdateAGVDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AGVDto>({
      method: 'PUT',
      url: `/api/app/a-gV/${id}`,
      body: model,
    },
    { apiName: this.apiName,...config });
  

  upload = (file: FormData, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/a-gV/upload',
      body: file,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
