import type { AuditedEntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';

export interface AGVDto extends AuditedEntityDto<string> {
  agvCode?: string;
  operationStatus?: string;
  description?: string;
}

export interface CreateUpdateAGVDto {
  agvCode?: string;
  operationStatus?: string;
  description?: string;
}

export interface GetAGVInputDto extends PagedAndSortedResultRequestDto {
  agvCode?: string;
  operationStatus?: string;
}
