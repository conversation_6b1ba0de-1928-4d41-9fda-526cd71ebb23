import type { AuditedEntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';

export interface CreateUpdateZoneDto {
  zoneName?: string;
  xmin: number;
  ymin: number;
  xmax: number;
  ymax: number;
}

export interface GetZoneInputDto extends PagedAndSortedResultRequestDto {
  zoneName?: string;
}

export interface ZoneDto extends AuditedEntityDto<string> {
  zoneName?: string;
  xmin: number;
  ymin: number;
  xmax: number;
  ymax: number;
}
