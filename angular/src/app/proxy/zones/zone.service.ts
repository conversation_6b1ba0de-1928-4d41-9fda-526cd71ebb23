import type { CreateUpdateZoneDto, GetZoneInputDto, ZoneDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ZoneService {
  apiName = 'Default';
  

  create = (input: CreateUpdateZoneDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ZoneDto>({
      method: 'POST',
      url: '/api/app/zone',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/zone/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ZoneDto>({
      method: 'GET',
      url: `/api/app/zone/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: GetZoneInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ZoneDto>>({
      method: 'GET',
      url: '/api/app/zone',
      params: { zoneName: input.zoneName, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getZoneDtos = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, ZoneDto[]>({
      method: 'GET',
      url: '/api/app/zone/zone-dtos',
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, input: CreateUpdateZoneDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ZoneDto>({
      method: 'PUT',
      url: `/api/app/zone/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
