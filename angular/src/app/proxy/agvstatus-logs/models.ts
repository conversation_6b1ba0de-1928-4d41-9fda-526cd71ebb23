import type { AuditedEntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';

export interface AGVStatusLogDto extends AuditedEntityDto<string> {
  agvid?: string;
  batteryLevel: number;
  operationStatus?: string;
  errorMessage?: string;
}

export interface CreateUpdateAGVStatusLogDto {
  agvid: string;
  batteryLevel: number;
  operationStatus?: string;
  errorMessage?: string;
}

export interface GetAGVStatusLogInputDto extends PagedAndSortedResultRequestDto {
  agvCode?: string;
  operationStatus?: string;
  batteryLevelMin?: number;
  batteryLevelMax?: number;
}
