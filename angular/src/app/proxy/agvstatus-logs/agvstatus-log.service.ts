import type { AGVStatusLogDto, CreateUpdateAGVStatusLogDto, GetAGVStatusLogInputDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AGVStatusLogService {
  apiName = 'Default';
  

  create = (input: CreateUpdateAGVStatusLogDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AGVStatusLogDto>({
      method: 'POST',
      url: '/api/app/a-gVStatus-log',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/a-gVStatus-log/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AGVStatusLogDto>({
      method: 'GET',
      url: `/api/app/a-gVStatus-log/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: GetAGVStatusLogInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<AGVStatusLogDto>>({
      method: 'GET',
      url: '/api/app/a-gVStatus-log',
      params: { agvCode: input.agvCode, operationStatus: input.operationStatus, batteryLevelMin: input.batteryLevelMin, batteryLevelMax: input.batteryLevelMax, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, input: CreateUpdateAGVStatusLogDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AGVStatusLogDto>({
      method: 'PUT',
      url: `/api/app/a-gVStatus-log/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
