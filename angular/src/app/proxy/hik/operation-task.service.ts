import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class OperationTaskService {
  apiName = 'Default';
  

  cancelTask = (taskCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/operation/cancel-task',
      params: { taskCode },
    },
    { apiName: this.apiName,...config });
  

  complateTask = (taskCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/operation/complate-task',
      params: { taskCode },
    },
    { apiName: this.apiName,...config });
  

  continueTask = (taskCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/operation/continue-task',
      params: { taskCode },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
