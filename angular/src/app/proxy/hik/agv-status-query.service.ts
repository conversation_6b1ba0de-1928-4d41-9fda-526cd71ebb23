import type { AGVStatusDto, AndonDetailDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { Flag } from '../flags/models';

@Injectable({
  providedIn: 'root',
})
export class AgvStatusQueryService {
  apiName = 'Default';
  

  findSurroundingPointsByPointsAndX1AndY1 = (points: Flag[], x1: number, y1: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string[]>({
      method: 'POST',
      url: '/api/app/agv-status-query/find-surrounding-points',
      params: { x1, y1 },
      body: points,
    },
    { apiName: this.apiName,...config });
  

  getAll = (zoneId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AGVStatusDto[]>({
      method: 'GET',
      url: '/api/app/agv-status-query',
      params: { zoneId },
    },
    { apiName: this.apiName,...config });
  

  getStatusTaskByRobot = (robot: AGVStatusDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AndonDetailDto>({
      method: 'GET',
      url: '/api/app/agv-status-query/status-task',
      params: { battery: robot.battery, exclType: robot.exclType, mapCode: robot.mapCode, online: robot.online, robotCode: robot.robotCode, robotIp: robot.robotIp, speed: robot.speed, status: robot.status, stop: robot.stop, timestamp: robot.timestamp, posX: robot.posX, posY: robot.posY },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
