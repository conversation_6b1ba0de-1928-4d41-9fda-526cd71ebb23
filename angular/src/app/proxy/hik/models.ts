import type { AGVTaskDto } from '../agvtasks/models';

export interface AGVStatusDto {
  battery?: string;
  exclType?: string;
  mapCode?: string;
  online: boolean;
  robotCode?: string;
  robotIp?: string;
  speed?: string;
  status?: string;
  stop?: string;
  timestamp: number;
  posX: number;
  posY: number;
}

export interface AndonDetailDto {
  listFlag: string[];
  agvTask: AGVTaskDto;
  posX?: number;
  posY?: number;
  robotIp?: string;
  status?: string;
  battery?: string;
}

export interface HikRequestBase {
  reqCode?: string;
}

export interface HikResponse<T> {
  code?: string;
  data: object;
  message?: string;
  reqCode?: string;
}

export interface WarnCallbackItem {
  robotCode?: string;
  beginTime?: string;
  warnContent?: string;
  taskCode?: string;
}

export interface WarnCallbackRequestDto extends HikRequestBase {
  data: WarnCallbackItem[];
}

export interface WarnCallbackResponse {
  code?: string;
  message?: string;
  reqCode?: string;
}
