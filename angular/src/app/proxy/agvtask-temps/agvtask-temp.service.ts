import type { AGVTaskTempDto, CreateUpdateAGVTaskTempDto, GetAGVTaskTempInputDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AGVTaskTempService {
  apiName = 'Default';
  

  create = (input: CreateUpdateAGVTaskTempDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AGVTaskTempDto>({
      method: 'POST',
      url: '/api/app/a-gVTask-temp',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/a-gVTask-temp/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AGVTaskTempDto>({
      method: 'GET',
      url: `/api/app/a-gVTask-temp/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: GetAGVTaskTempInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<AGVTaskTempDto>>({
      method: 'GET',
      url: '/api/app/a-gVTask-temp',
      params: { taskType: input.taskType, robotTaskCode: input.robotTaskCode, taskStatus: input.taskStatus, dateFrom: input.dateFrom, dateTo: input.dateTo, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  retry = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/a-gVTask-temp/${id}/retry`,
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, input: CreateUpdateAGVTaskTempDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AGVTaskTempDto>({
      method: 'PUT',
      url: `/api/app/a-gVTask-temp/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
