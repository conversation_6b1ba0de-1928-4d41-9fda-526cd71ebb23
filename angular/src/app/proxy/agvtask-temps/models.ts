import type { AuditedEntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';

export interface AGVTaskTempDto extends AuditedEntityDto<string> {
  pdaOrderRecordID?: string;
  taskType?: string;
  targetRoute?: string;
  initPriotiry: number;
  robotTaskCode?: string;
  agvid?: string;
  taskStatus?: string;
  taskCreatedAt?: string;
  taskCompletedAt?: string;
  content?: string;
  retry: number;
}

export interface CreateUpdateAGVTaskTempDto {
  pdaOrderRecordID: string;
  taskType?: string;
  targetRoute?: string;
  initPriotiry: number;
  robotTaskCode?: string;
  agvid?: string;
  taskStatus?: string;
  taskCreatedAt?: string;
  taskCompletedAt?: string;
  content?: string;
  retry: number;
}

export interface GetAGVTaskTempInputDto extends PagedAndSortedResultRequestDto {
  taskType?: string;
  robotTaskCode?: string;
  taskStatus?: string;
  dateFrom?: string;
  dateTo?: string;
}
