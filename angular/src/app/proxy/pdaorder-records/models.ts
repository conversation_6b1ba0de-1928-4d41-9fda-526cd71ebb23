import type { AuditedEntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';

export interface AddPDAOrderRecord {
  content: string[];
  agvType?: string;
  deviceCode?: string;
  agvCode?: string;
  containerType?: string;
  listPoint: string[];
}

export interface CreateUpdatePDAOrderRecordDto {
  content?: string;
  agvType?: string;
  deviceCode: string;
  agvCode?: string;
  sendContent?: string;
  pdaOrderRecordID?: string;
  listPoint?: string;
  status?: string;
  callSize?: string;
  router?: string;
  containerType?: string;
}

export interface GetPDAOrderRecordInputDto extends PagedAndSortedResultRequestDto {
  agvType?: string;
  deviceCode?: string;
  agvCode?: string;
  callSize?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface PDAOrderRecordDto extends AuditedEntityDto<string> {
  content?: string;
  agvType?: string;
  deviceCode?: string;
  agvCode?: string;
  sendContent?: string;
  pdaOrderRecordID?: string;
  listPoint?: string;
  status?: string;
  callSize?: string;
  router?: string;
  containerType?: string;
}
