import type { AddPDAOrderRecord } from './models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { HikResponse } from '../hik/models';

@Injectable({
  providedIn: 'root',
})
export class AddTaskService {
  apiName = 'Default';
  

  addTaskByInput = (input: AddPDAOrderRecord, config?: Partial<Rest.Config>) =>
    this.restService.request<any, HikResponse<string>>({
      method: 'POST',
      url: '/api/app/task/add-task',
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
