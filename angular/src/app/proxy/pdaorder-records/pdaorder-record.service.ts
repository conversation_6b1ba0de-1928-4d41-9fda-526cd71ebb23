import type { CreateUpdatePDAOrderRecordDto, GetPDAOrderRecordInputDto, PDAOrderRecordDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class PDAOrderRecordService {
  apiName = 'Default';
  

  create = (input: CreateUpdatePDAOrderRecordDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PDAOrderRecordDto>({
      method: 'POST',
      url: '/api/app/p-dAOrder-record',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/p-dAOrder-record/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PDAOrderRecordDto>({
      method: 'GET',
      url: `/api/app/p-dAOrder-record/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: GetPDAOrderRecordInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<PDAOrderRecordDto>>({
      method: 'GET',
      url: '/api/app/p-dAOrder-record',
      params: { agvType: input.agvType, deviceCode: input.deviceCode, agvCode: input.agvCode, callSize: input.callSize, dateFrom: input.dateFrom, dateTo: input.dateTo, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, input: CreateUpdatePDAOrderRecordDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PDAOrderRecordDto>({
      method: 'PUT',
      url: `/api/app/p-dAOrder-record/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
