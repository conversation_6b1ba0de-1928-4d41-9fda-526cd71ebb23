import type { CreateUpdateRouterDataDto, GetRouterDataInputDto, RouterDataDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class RouterDataService {
  apiName = 'Default';
  

  create = (model: CreateUpdateRouterDataDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, RouterDataDto>({
      method: 'POST',
      url: '/api/app/router-data',
      body: model,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/router-data/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, RouterDataDto>({
      method: 'GET',
      url: `/api/app/router-data/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: GetRouterDataInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<RouterDataDto>>({
      method: 'GET',
      url: '/api/app/router-data',
      params: { routerName: input.routerName, routerType: input.routerType, router: input.router, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, model: CreateUpdateRouterDataDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, RouterDataDto>({
      method: 'PUT',
      url: `/api/app/router-data/${id}`,
      body: model,
    },
    { apiName: this.apiName,...config });
  

  upload = (file: FormData, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/router-data/upload',
      body: file,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
