import type { AuditedEntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';

export interface CreateUpdateRouterDataDto {
  routerName: string;
  routerType: string;
  contentData: string;
}

export interface GetRouterDataInputDto extends PagedAndSortedResultRequestDto {
  routerName?: string;
  routerType?: string;
  router?: string;
}

export interface RouterDataDto extends AuditedEntityDto<string> {
  routerName: string;
  routerType: string;
  contentData: string;
}
