import type { AuditedEntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';

export interface CreateUpdateMaterialOrderDto {
  orderID?: string;
  partNumber: string;
  qrCode: string;
  description?: string;
  batchNumber?: string;
  quantity: number;
  unit?: string;
  pointOfFeeding?: string;
  status?: string;
  timeStarted?: string;
  timeComplated?: string;
  complatedBy?: string;
  taskID?: string;
}

export interface GetMaterialOrderInputDto extends PagedAndSortedResultRequestDto {
  partNumber?: string;
  orderID?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface MaterialOrderDto extends AuditedEntityDto<string> {
  orderID?: string;
  partNumber?: string;
  qrCode?: string;
  description?: string;
  batchNumber?: string;
  quantity: number;
  unit?: string;
  pointOfFeeding?: string;
  status?: string;
  timeStarted?: string;
  timeComplated?: string;
  complatedBy?: string;
  taskID?: string;
}
