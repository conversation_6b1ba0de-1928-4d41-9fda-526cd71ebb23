import type { CreateUpdateMaterialOrderDto, GetMaterialOrderInputDto, MaterialOrderDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class MaterialOrderService {
  apiName = 'Default';
  

  create = (model: CreateUpdateMaterialOrderDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, MaterialOrderDto>({
      method: 'POST',
      url: '/api/app/material-order',
      body: model,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/material-order/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, MaterialOrderDto>({
      method: 'GET',
      url: `/api/app/material-order/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: GetMaterialOrderInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<MaterialOrderDto>>({
      method: 'GET',
      url: '/api/app/material-order',
      params: { partNumber: input.partNumber, orderID: input.orderID, status: input.status, dateFrom: input.dateFrom, dateTo: input.dateTo, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, input: CreateUpdateMaterialOrderDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, MaterialOrderDto>({
      method: 'PUT',
      url: `/api/app/material-order/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
