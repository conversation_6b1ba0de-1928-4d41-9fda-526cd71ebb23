import type { CreateUpdateFlagDto, FlagDto, GetFlagInputDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class FlagService {
  apiName = 'Default';
  

  create = (input: CreateUpdateFlagDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, FlagDto>({
      method: 'POST',
      url: '/api/app/flag',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/flag/${id}`,
    },
    { apiName: this.apiName,...config });
  

  download = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, Blob>({
      method: 'POST',
      responseType: 'blob',
      url: '/api/app/flag/download',
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, FlagDto>({
      method: 'GET',
      url: `/api/app/flag/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: GetFlagInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<FlagDto>>({
      method: 'GET',
      url: '/api/app/flag',
      params: { flagName: input.flagName, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: string, input: CreateUpdateFlagDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, FlagDto>({
      method: 'PUT',
      url: `/api/app/flag/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });
  

  upload = (file: FormData, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/flag/upload',
      body: file,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
