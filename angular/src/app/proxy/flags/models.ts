import type { AuditedEntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import type { AuditedAggregateRoot } from '../volo/abp/domain/entities/auditing/models';

export interface CreateUpdateFlagDto {
  flagName?: string;
  x: number;
  y: number;
}

export interface FlagDto extends AuditedEntityDto<string> {
  flagName?: string;
  x: number;
  y: number;
}

export interface GetFlagInputDto extends PagedAndSortedResultRequestDto {
  flagName?: string;
}

export interface Flag extends AuditedAggregateRoot<string> {
  flagName?: string;
  x: number;
  y: number;
}
