import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ErrorHandlingRoutingModule } from './error-handling-routing.module';
import { ErrorHandlingComponent } from './error-handling.component';
import { SharedModule } from '../shared/shared.module';
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';


@NgModule({
  declarations: [
    ErrorHandlingComponent,
  ],
  imports: [
    CommonModule,
    ErrorHandlingRoutingModule,
    SharedModule,
    NgbDatepickerModule
  ]
})
export class ErrorHandlingModule { }
