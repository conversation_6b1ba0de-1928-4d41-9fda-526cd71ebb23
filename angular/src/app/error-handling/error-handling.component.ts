import { ListService, PagedResultDto } from '@abp/ng.core';
import { Component, OnInit } from '@angular/core';
import { ErrorHandlingDto, ErrorHandlingService } from '../proxy/error-handlings';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { NgbDateNativeAdapter, NgbDateAdapter } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationService, Confirmation } from '@abp/ng.theme.shared';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-error-handling',
  templateUrl: './error-handling.component.html',
  styleUrls: ['./error-handling.component.scss'],
  standalone: false,
  providers: [ListService,
    { provide: NgbDateAdapter, useClass: NgbDateNativeAdapter }],
})
export class ErrorHandlingComponent implements OnInit {
  errorHandling = { items: [], totalCount: 0 } as PagedResultDto<ErrorHandlingDto>;
  selectedErrorHandling = {} as ErrorHandlingDto;
  form: FormGroup;
  filterForm: FormGroup;
  isModalOpen = false;
statusOptions = [
  { value: 'Abnormal task', label: '3' },
  { value: 'Rack not recognized', label: '11' },
  { value: 'Rack angle deflected', label: '12' },
  { value: 'Motion library exception', label: '13' },
  { value: 'Rack code unrecognized', label: '14' },
  { value: 'Rack code mismatch', label: '15' },
  { value: 'Lifting exception', label: '16' },
  { value: 'Charging station exception', label: '17' },
  { value: 'Battery not charging', label: '18' },
  { value: 'Charging direction error', label: '20' },
  { value: 'Platform command error', label: '21' },
  { value: 'Abnormal unloading', label: '23' },
  { value: 'Rack position deviated', label: '24' },
  { value: 'Robot not in the block zone', label: '25' },
  { value: 'Retry putting down failed', label: '26' },
  { value: 'Incorrect rack location', label: '27' },
  { value: 'Low battery for lifting', label: '28' },
  { value: 'Robot reversing angle deflected', label: '29' },
  { value: 'Lifting without rack', label: '30' },
  { value: 'Blocking zone failed', label: '31' },
  { value: 'Rotation request temporarily failed', label: '33' },
  { value: 'Map switching code unrecognized', label: '34' }
];


  statusHandlingOptions = [
    '','0','1'
];
  statusUpdateOptions = [
    '0','1'
];

  constructor(
    public readonly list: ListService,
    private fb: FormBuilder,
    private errorHandlingService: ErrorHandlingService,
    private confirmation: ConfirmationService,
    private datePipe: DatePipe
  ) {

    // Khởi tạo form lọc
    // Khởi tạo form lọc
    const date = new Date();
    this.filterForm = this.fb.group({
      agvCode: [''],
      errorCode: [''],
      dateFrom: [date],
      dateTo: [date]
    });

    // Kết nối dữ liệu với bộ lọc
    this.list.hookToQuery((query) => this.getData(query)).subscribe((res) => {
      this.errorHandling = res;
    });
  }

  ngOnInit(): void {
    console.log('ErrorHandlingComponent initialized');
  }

  getData(query) {
    const filter = this.filterForm.value;

    return this.errorHandlingService.getList({
      ...query,
      agvCode: filter.agvCode || undefined,
      errorCode: filter.errorCode || undefined,
      dateFrom: filter.dateFrom ? this.datePipe.transform(filter.dateFrom, 'yyyy-MM-dd') : null,
      dateTo: filter.dateTo ? this.datePipe.transform(filter.dateTo, 'yyyy-MM-dd') : null
    });
  }

  search() {
    this.list.get();
  }

  resetFilters() {
    this.filterForm.reset();
    this.list.get();
  }

  createErrorHandling() {
    this.selectedErrorHandling = {} as ErrorHandlingDto;
    this.buildForm();
    this.isModalOpen = true;
  }

  editErrorHandling(id: string) {
    this.errorHandlingService.get(id).subscribe((errorHandling) => {
      this.selectedErrorHandling = errorHandling;
      this.buildForm();
      this.isModalOpen = true;
    });
  }
  
buildForm() {
  this.form = this.fb.group({
    agvCode: [{ value: this.selectedErrorHandling.agvCode || '', disabled: true }, Validators.required],
    errorCode: [{ value: this.selectedErrorHandling.errorCode || '', disabled: true }, Validators.required],
    errorAt: [{
      value: this.selectedErrorHandling.errorAt ? new Date(this.selectedErrorHandling.errorAt) : null,
      disabled: true
    }, Validators.required],
    fixStatus: [this.selectedErrorHandling.fixStatus || '', Validators.required],
    fixDescription: [this.selectedErrorHandling.fixDescription || '', Validators.required],
    fixedAt: [{
      value: this.selectedErrorHandling.fixedAt ? new Date(this.selectedErrorHandling.fixedAt) : null,
      disabled: false
    }, Validators.required],
    fixedBy: [this.selectedErrorHandling.fixedBy || '', Validators.required],
  });
}


  save() {
    if (this.form.invalid) return;

    const request = this.selectedErrorHandling.id
      ? this.errorHandlingService.update(this.selectedErrorHandling.id, this.form.getRawValue())
      : this.errorHandlingService.create(this.form.value);

    request.subscribe(() => {
      this.isModalOpen = false;
      this.form.reset();
      this.list.get();
    });
  }

  deleteErrorHandling(id: string) {
    this.confirmation.warn('::AreYouSureToDelete', '::AreYouSure').subscribe((status) => {
      if (status === Confirmation.Status.confirm) {
        this.errorHandlingService.delete(id).subscribe(() => this.list.get());
      }
    });
  }

    getStatusClass(status: string): string {
  switch (status) {
    case '1':
      return 'badge bg-success'; // xanh lá
    case '0':
      return 'badge bg-danger'; // đỏ
    default:
      return 'badge bg-warning text-dark'; // mặc định
  }
}

  getAgvStatusClass(status: string): string {
  switch (status) {
    case '3':
    case '4':
      return 'badge bg-secondary'; // xám
    case '1':
      return 'badge bg-success'; // xanh lá
    case '2':
    case '6':
    case '7':
    case '8':
      return 'badge bg-warning text-dark'; // đỏ
    default:
      return 'badge bg-danger'; // mặc định
  }
}
}
