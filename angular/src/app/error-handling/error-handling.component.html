


<div class="card">
  <div class="card-header">
    <div class="row">
      <div class="col col-md-6">
        <h5 class="card-title">{{ '::Menu:ErrorHandling' | abpLocalization }}</h5>
      </div>      
    </div>
  </div>

  <div class="card-body">
    
<form [formGroup]="filterForm" (ngSubmit)="search()" class="mb-3 row align-items-end">
  <div class="col-md-3">
    <label>{{ '::AGVCode' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="agvCode" />
  </div>
  <div class="col-md-3">
    <label>{{ '::FixStatus' | abpLocalization }}</label>
    <select class="form-control" formControlName="errorCode">
      <option *ngFor="let status of statusHandlingOptions" [ngValue]="status">
        {{ status === '' ? ('::All' | abpLocalization) : ('::Enum:HandlingStatus.' + status) | abpLocalization }}
      </option>
    </select>
  </div>
  <div class="col-md-2">
        <label>{{ '::DateFrom' | abpLocalization }}</label>
        <input
            #datepicker1="ngbDatepicker"
            class="form-control"
            name="datepickerFrom"
            formControlName="dateFrom"
            ngbDatepicker
            (click)="datepicker1.toggle()"
          />
      </div>
        <div class="col-md-2">
        <label>{{ '::DateTo' | abpLocalization }}</label>
        <input
            #datepicker2="ngbDatepicker"
            class="form-control"
            name="datepickerTo"
            formControlName="dateTo"
            ngbDatepicker
            (click)="datepicker2.toggle()"
          />
      </div>
  <div class="col-md-2 d-flex gap-2">
    <button class="btn btn-primary btn-sm w-auto" type="submit">
      <i class="fa fa-search me-1"></i>{{ '::Search' | abpLocalization }}
    </button>
    <button class="btn btn-secondary btn-sm w-auto" type="button" (click)="resetFilters()">
      <i class="fa fa-times me-1"></i>{{ '::Clear' | abpLocalization }}
    </button>
  </div>
</form>
    <ngx-datatable
      [rows]="errorHandling.items"
      [count]="errorHandling.totalCount"
      [list]="list" default
       [columnMode]="'force'"
      >

      <!-- Actions Column -->
      <ngx-datatable-column
        [name]="'::Actions' | abpLocalization"
        [maxWidth]="150"
        [sortable]="false"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div ngbDropdown container="body" class="d-inline-block">
            <button class="btn btn-primary btn-sm dropdown-toggle" ngbDropdownToggle>
              <i class="fa fa-cog me-1"></i>{{ '::Actions' | abpLocalization }}
            </button>
            <div ngbDropdownMenu>
              <button *abpPermission="'SOR.ErrorHandlings.Update'" ngbDropdownItem (click)="editErrorHandling(row.id)">
                {{ '::Edit' | abpLocalization }}
              </button>
            </div>
          </div>
        </ng-template>
      </ngx-datatable-column>

      <ngx-datatable-column
        [name]="'::AGVCode' | abpLocalization"
        [flexGrow]="1" prop="agvCode"
      ></ngx-datatable-column>
      
      <ngx-datatable-column
        [name]="'::ErrorCode' | abpLocalization"
        [flexGrow]="1" prop="errorCode"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span class="status" [ngClass]="getAgvStatusClass(row.errorCode)">
            {{ ('::Enum:AGVStatus.' + row.errorCode) | abpLocalization }}
          </span>
        </ng-template>
      </ngx-datatable-column>

        <ngx-datatable-column [name]="'::ErrorAt' | abpLocalization" [flexGrow]="1" prop="errorAt" [flexGrow]="1">
    <ng-template let-row="row" ngx-datatable-cell-template>
      {{ row.errorAt | date: 'dd/MM/yyyy HH:mm:ss' }}
    </ng-template>
  </ngx-datatable-column>


            <ngx-datatable-column
        [name]="'::FixStatus' | abpLocalization"
        [flexGrow]="1" prop="fixStatus"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
           <span class="status" [ngClass]="getStatusClass(row.fixStatus)">
            {{ ('::Enum:HandlingStatus.' + row.fixStatus) | abpLocalization }}
          </span>
        </ng-template>
      </ngx-datatable-column>

            <ngx-datatable-column
        [name]="'::FixedBy' | abpLocalization"
        [flexGrow]="1" prop="fixedBy"
      ></ngx-datatable-column>

  <ngx-datatable-column [name]="'::FixedAt' | abpLocalization" [flexGrow]="1" prop="fixedAt" [flexGrow]="1">
    <ng-template let-row="row" ngx-datatable-cell-template>
      {{ row.fixedAt | date: 'dd/MM/yyyy HH:mm:ss' }}
    </ng-template>
  </ngx-datatable-column>

        <ngx-datatable-column
        [name]="'::FixDescription' | abpLocalization"
        [flexGrow]="1" prop="fixDescription"
      ></ngx-datatable-column>
    </ngx-datatable>

  </div>
</div>

<!-- Modal -->
<abp-modal [(visible)]="isModalOpen">
  <ng-template #abpHeader>
    <h3>{{ (selectedErrorHandling?.id ? '::Permission:ErrorHandlings.Update' : '::Permission:ErrorHandlings.Create') | abpLocalization }}</h3>
  </ng-template>

    <ng-template #abpBody>
      <form [formGroup]="form" (ngSubmit)="save()">
        <div class="mt-2">
          <label for="agv-code">{{ '::AGVCode' | abpLocalization }}</label><span> * </span>
          <input
            type="text"
            id="agv-code"
            class="form-control"
            formControlName="agvCode"
            
          />
        </div>

        <div class="mt-2">
          <label for="error-code">{{'::ErrorCode' | abpLocalization}}</label><span> * </span>
          <select
            class="form-control"
            id="error-code"
            formControlName="errorCode"
            
          >
            <option
              *ngFor="let status of statusOptions"
              [ngValue]="status.label"
            >
              {{ ('::Enum:AGVStatus.' + status.label) | abpLocalization }}
            </option>
          </select>
        </div>

          <div class="mt-2">
          <label>{{ '::ErrorAt' | abpLocalization }}</label><span> * </span>
          <input
            #datepicker3="ngbDatepicker"
            class="form-control"
            name="errorAt"
            formControlName="errorAt"
            ngbDatepicker
            (click)="datepicker3.toggle()"
            
          />
        </div>

        <div class="mt-2">
          <label for="fix-status">{{'::FixStatus' | abpLocalization}}</label><span> * </span>
          <select
            class="form-control"
            id="fix-status"
            formControlName="fixStatus"
          >
            <option
              *ngFor="let status of statusUpdateOptions"
              [ngValue]="status"
            >
              {{ ('::Enum:HandlingStatus.' + status) | abpLocalization }}
            </option>
          </select>
        </div>

        <div class="mt-2">
          <label for="fixed-by">{{ '::FixedBy' | abpLocalization }}</label><span> * </span>
          <input
            type="text"
            id="fixed-by"
            class="form-control"
            formControlName="fixedBy"
            autofocus
          />
        </div>

        <div class="mt-2">
          <label>{{ '::FixedAt' | abpLocalization }}</label><span> * </span>
          <input
            #datepicker4="ngbDatepicker"
            class="form-control"
            name="fixedAt"
            formControlName="fixedAt"
            ngbDatepicker
            (click)="datepicker4.toggle()"
          />
        </div>

        <div class="mt-2">
          <label for="fix-description">{{'::FixDescription' | abpLocalization}}</label><span> * </span>
          <textarea
            id="fix-description"
            class="form-control"
            rows="3"
            formControlName="fixDescription"
          ></textarea>
        </div>
      </form>
    </ng-template>


  <ng-template #abpFooter>
    <button type="button" class="btn btn-secondary" abpClose>
      {{ '::Close' | abpLocalization }}
    </button>
    <button class="btn btn-primary" (click)="save()" [disabled]="form.invalid">
      <i class="fa fa-check mr-1"></i>
      {{ '::Save' | abpLocalization }}
    </button>
  </ng-template>
</abp-modal>
