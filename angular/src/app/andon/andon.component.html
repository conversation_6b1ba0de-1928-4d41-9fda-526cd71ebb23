<div class="andon-container">
  <div class="status-panel">
    <h1 *ngIf="selectedZone" class="status-item">Zone: {{ selectedZone.zoneName }}</h1>
    <!-- <div class="status-item online">Online: {{ onlineCount }}</div>
    <div class="status-item offline">Offline: {{ offlineCount }}</div> -->
    <div class="status-item time">{{ timer }}</div>
  </div>

  <div class="grid">
    <div
      *ngFor="let robot of robots"
      class="card"
      (click)="openAgvDetail(robot)"
      [ngClass]="{
        'online-idle': robot.online && robot.displayStatus === 'Idle',
        'online-running': robot.online && robot.displayStatus === 'Running',
        'online-charging': robot.online && robot.displayStatus === 'Charging',
        'online-fault': robot.online && robot.displayStatus === 'Fault',
        'online-stop': robot.online && robot.displayStatus === 'Stop',
        'offline': !robot.online
      }"
    >
      <div class="card-header">
        {{ robot.robotCode }}
      </div>
      <div class="card-body">
        <div>{{ robot.displayStatus }}</div>
      </div>
    </div>
  </div>
</div>


<abp-modal [(visible)]="isAgvModalOpen">
  <ng-template #abpHeader>
    <h3>{{ '::AgvDetail' | abpLocalization }}</h3>
  </ng-template>

  <ng-template #abpBody>
    <p><strong>{{ '::RobotIp' | abpLocalization }}:</strong> {{ selectedAgvTask.robotIp }}</p>
    <p><strong>{{ '::Status' | abpLocalization }}:</strong><span class="status" [ngClass]="getAgvStatusClass(selectedAgvTask.status)">
        {{ ('::Enum:AGVStatus.' + selectedAgvTask.status) | abpLocalization }}
      </span></p>


            <p><strong>{{ '::X' | abpLocalization }}:</strong> {{ selectedAgvTask.posX }}</p>
    <p><strong>{{ '::Y' | abpLocalization }}:</strong> {{ selectedAgvTask.posY }}</p>
    <h5>{{ '::Location' | abpLocalization }}</h5>
    <div class="location-grid">
      <div class="cell">{{ selectedAgvTask.listFlag[0] }}</div>
      <div class="cell"></div> <!-- Top -->
      <div class="cell">{{ selectedAgvTask.listFlag[2] }}</div>

      <div class="cell"></div> <!-- Left -->
      <div class="cell agv-center">AGV</div>
      <div class="cell"></div> <!-- Right -->

      <div class="cell">{{ selectedAgvTask.listFlag[3] }}</div>
      <div class="cell"></div> <!-- Bottom -->
      <div class="cell">{{ selectedAgvTask.listFlag[1] }}</div>
    </div>

  <div *ngIf="selectedAgvTask.agvTask.agvID; else noTask">
    <h5>{{ '::LatestTask' | abpLocalization }}</h5>
    <p><strong>{{ '::AGVCode' | abpLocalization }}:</strong> {{ selectedAgvTask.agvTask.agvID }}</p>
    <p><strong>{{ '::PDAOrderRecordID' | abpLocalization }}:</strong> {{ selectedAgvTask.agvTask.pdaOrderRecordID }}</p>
    <p><strong>{{ '::Router' | abpLocalization }}:</strong> {{ selectedAgvTask.agvTask.router }}</p>
    <p><strong>{{ '::TargetRoute' | abpLocalization }}:</strong> {{ selectedAgvTask.agvTask.targetRoute }}</p>
    <p><strong>{{ '::TaskStatus' | abpLocalization }}:</strong><span class="taskStatus" [ngClass]="getTaskStatusClass(selectedAgvTask.agvTask.taskStatus)">
            {{ ('::Enum:AgvTaskStatus.' + selectedAgvTask.agvTask.taskStatus) | abpLocalization }}
          </span></p>
    <p><strong>{{ '::TaskCreatedAt' | abpLocalization }}:</strong> {{ selectedAgvTask.agvTask.taskCreatedAt | date: 'dd/MM/yyyy HH:mm:ss' }}</p>
    <p><strong>{{ '::TaskCompletedAt' | abpLocalization }}:</strong> {{ selectedAgvTask.agvTask.taskCompletedAt | date: 'dd/MM/yyyy HH:mm:ss' }}</p>
  </div>





  <ng-template #noTask>
    <div class="text-center text-muted py-3">
      {{ '::NoAgvTaskSelected' | abpLocalization }}
    </div>
  </ng-template>


</ng-template>


  <ng-template #abpFooter>
    <button class="btn btn-secondary" abpClose>
      Đóng
    </button>
  </ng-template>
</abp-modal>
