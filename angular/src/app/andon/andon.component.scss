/* Main container */
.andon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--background); 
  color: var(--primary);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden; /* Prevent container scrolling */
}

/* Header styling */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
  font-size: 2rem;
  font-weight: bold;
  color: #00ffe7;
  text-shadow: 0 0 8px rgba(0, 255, 231, 0.5);
}

/* Section title */
.section {
  width: 100%;
  text-align: center;
  font-size: 1.75rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: #ffffff;
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.4);
}

/* Grid container - auto-filling grid */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  grid-auto-rows: 120px;
  gap: 12px; /* Tăng khoảng cách từ 8px lên 15px */
  width: 100%;
  height: calc(100vh - 180px);
  overflow-y: auto;
  padding: 8px; /* Tăng padding từ 5px lên 8px để tạo khoảng cách với mép ngoài */
  scrollbar-width: thin;
  scrollbar-color: #555 var(--background);
}

/* Scrollbar styling for Chrome/Edge/Safari */
.grid::-webkit-scrollbar {
  width: 8px;
}

.grid::-webkit-scrollbar-track {
  background: #222;
  border-radius: 4px;
}

.grid::-webkit-scrollbar-thumb {
  background-color: #555;
  border-radius: 4px;
}

/* Card styling */
.card {
  height: 120px; /* Giảm từ 150px xuống 120px để khớp với grid-auto-rows */
  border-radius: 6px; /* Giảm border radius */
  padding: 12px; /* Giảm padding */
  font-weight: bold;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

.card-header {
  height: 40px; /* Giảm từ 45px xuống 35px */
  display: flex;
  align-items: center;
  font-size: 2.2rem; /* Giảm từ 1.8rem */
  text-align: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 3px; /* Giảm margin */
}

.card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  text-align: center;
  font-size: 1.25rem; /* Giảm từ 1.1rem */
  line-height: 1.1;
  padding: 2px 0;
}

.card:hover {
  transform: scale(1.02); /* Giảm scale effect từ 1.03 xuống 1.02 */
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

/* STATUS COLORS */
.online-idle {
  background-color: #508bf1;
  color: #ffffff !important;
}

.online-running {
  background-color: #00c851;
  color: #ffffff;
}

.online-charging {
  background-color: #ffeb3b;
  color: #000000;
}

.online-fault {
  background-color: #e53935;
  color: #ffffff !important;
}

.online-stop {
  background-color: #ff6f00;
  color: #ffffff;
}

.offline {
  background-color: #9e9e9e;
  color: #ffffff;
}

/* FOOTER */
.footer {
  margin-top: 20px;
  text-align: center;
  font-size: 1.2rem;
  color: #888;
  width: 100%;
}

.team-alert {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ff1744;
  text-shadow: 0 0 6px rgba(255, 23, 68, 0.5);
}

/* Status panel styling */
.status-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(30, 30, 30, 0.8);
  border: 1px solid #333;
  border-radius: 12px;
  padding: 10px 25px;
  margin-bottom: 15px;
  width: 100%;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

.status-item {
  font-family: 'Segoe UI', sans-serif;
  font-weight: bold;
  font-size: 1.6rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.status-item.online {
  color: #00ff7f;
}

.status-item.offline {
  color: #ff6347;
}

.status-item.time {
  color: var(--primary);
}

/* Card styling - Giữ nguyên cho màn hình FullHD */
.card {
  height: 120px;
  border-radius: 6px;
  padding: 12px;
  font-weight: bold;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

.card-header {
  height: 40px;
  display: flex;
  align-items: center;
  font-size: 2.2rem;
  text-align: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 3px;
}

.card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  text-align: center;
  font-size: 1.25rem;
  line-height: 1.1;
  padding: 2px 0;
}

/* Responsive adjustments */
/* Màn hình cực lớn */
@media (min-width: 2560px) {
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    grid-auto-rows: 160px;
  }
  
  .card {
    height: 160px;
    padding: 15px;
  }
  
  .card-header {
    height: 50px;
    font-size: 2.5rem;
  }
  
  .card-body {
    font-size: 1.4rem;
    line-height: 1.2;
  }
}

/* Màn hình lớn */
@media (max-width: 1800px) and (min-width: 1401px) {
  .card-header {
    font-size: 2.0rem;
  }
  
  .card-body {
    font-size: 1.15rem;
  }
}

/* Màn hình trung bình */
@media (max-width: 1400px) {
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }
  
  .card {
    padding: 10px;
  }
  
  .card-header {
    font-size: 1.7rem;
    height: 38px;
  }
  
  .status-item {
    font-size: 1.4rem;
  }
  
  .card-body {
    font-size: 1rem;
  }
}

/* Màn hình nhỏ hơn */
@media (max-width: 1200px) {
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  }
  
  .card {
    padding: 8px;
  }
  
  .card-header {
    font-size: 1.5rem;
    height: 35px;
    margin-bottom: 2px;
  }
  
  .card-body {
    font-size: 0.9rem;
    line-height: 1.05;
  }
}

/* Tablet */
@media (max-width: 900px) {
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
  }
  
  .header {
    font-size: 1.6rem;
  }
  
  .section {
    font-size: 1.5rem;
  }
  
  .card {
    height: 110px;
    padding: 6px;
  }
  
  .card-header {
    font-size: 1.3rem;
    height: 32px;
    margin-bottom: 1px;
  }
  
  .card-body {
    font-size: 0.8rem;
    line-height: 1.0;
  }
  
  .card:hover {
    transform: scale(1.01); /* Giảm scale effect để không che phủ cards khác */
  }
}

/* Mobile */
@media (max-width: 600px) {
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 6px;
  }
  
  .status-panel {
    flex-direction: column;
    gap: 10px;
    padding: 8px 15px;
  }
  
  .card {
    height: 100px;
    padding: 5px;
  }
  
  .card-header {
    font-size: 1.1rem;
    height: 28px;
    margin-bottom: 0;
  }
  
  .card-body {
    font-size: 0.75rem;
    padding: 0;
  }
}

.badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.85rem;
  font-weight: 500;
  display: inline-block;
}

.bg-success { background-color: #198754; color: white; }
.bg-danger { background-color: #dc3545; color: white; }
.bg-warning { background-color: #ffc107; color: black; }
.bg-primary { background-color: #0d6efd; color: white; }
.bg-secondary { background-color: #6c757d; color: white; }
.bg-light { background-color: #f8f9fa; color: #000; }

.location-grid {
  display: grid;
  grid-template-columns: 60px 60px 60px;
  grid-template-rows: 60px 60px 60px;
  gap: 5px;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}

.cell {
  border: 1px solid #ccc;
  text-align: center;
  vertical-align: middle;
  line-height: 60px;
  background-color: #f8f9fa;
  font-weight: 500;
  font-size: 14px;
  border-radius: 6px;
}

.agv-center {
  background-color: #007bff;
  color: white;
  font-weight: bold;
}
