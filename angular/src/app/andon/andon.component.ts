import { Component, OnInit } from '@angular/core';
import * as signalR from '@microsoft/signalr';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { RoutesService, eLayoutType } from '@abp/ng.core'; // Import RoutesService
import { ActivatedRoute } from '@angular/router';
import { ZoneDto, ZoneService } from '../proxy/zones';
import { AGVStatusDto, AgvStatusQueryService, AndonDetailDto } from '../proxy/hik';
import { AGVTaskDto } from '../proxy/agvtasks';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../shared/shared.module';

@Component({
  selector: 'app-andon',
  templateUrl: './andon.component.html',
  styleUrls: ['./andon.component.scss'],
  standalone: true,
  imports: [CommonModule, SharedModule]
})
export class AndonComponent implements OnInit {
  robots: AGVStatusDto[] = [];
  zones: ZoneDto[] = []; // Danh sách các zone
  selectedZoneId: string = ''; // Zone được chọn
    selectedZone: ZoneDto | null = null; // Zone được chọn

  timer = '00:00:00';

  private hubConnection!: signalR.HubConnection;

  constructor(private route: ActivatedRoute, private routesService: RoutesService, private zoneService: ZoneService,
    private agvStatus: AgvStatusQueryService
  ) {} // Inject RoutesService

ngOnInit(): void {
  // First fetch zones, then process route params
  this.fetchZones();
  this.startSignalRConnection();
  this.startTimer();
}

fetchZones(): void {
  this.zoneService.getZoneDtos().subscribe({
    next: (data) => {
      // Add a default "All Zones" option as the first item
      const allZonesOption: ZoneDto = {
        id: 'all',
        zoneName: 'All Zones',
        xmin: 0,
        ymin: 0,
        xmax: 0,
        ymax: 0,
      };
      
      // Insert as first item in the array
      this.zones = [allZonesOption, ...data];
      
      this.addZonesToMenu(); // Add zones to menu
      
      // Process route params after zones are loaded
      this.handleRouteParams();
    },
    error: (error) => {
      console.error('Error fetching zones:', error);
    }
  });
}

handleRouteParams(): void {
  this.route.params.subscribe((params) => {
    const zoneId = params['zoneId'];
    
    if (zoneId && this.zones.length > 0) {
      // Try to find zone from URL parameter
      const foundZone = this.zones.find(zone => zone.id === zoneId);
      if (foundZone) {
        this.selectedZone = foundZone;
        this.selectedZoneId = foundZone.id;
      } else {
        // Fallback to first zone if specified zone not found
        this.selectedZone = this.zones[0];
        this.selectedZoneId = this.zones[0].id;
      }
    } else if (this.zones.length > 0) {
      // No zoneId in URL, select first zone as default
      this.selectedZone = this.zones[0];
      this.selectedZoneId = this.zones[0].id;
    }
    
    // Fetch data for selected zone only once
    if (this.selectedZoneId) {
      this.fetchDataByZone(this.selectedZoneId);
    }
  });
}

fetchDataByZone(zoneId: string): void {
  this.agvStatus.getAll(zoneId).subscribe(
    (data) => {
      this.robots = data.map(robot => ({
        ...robot,
        displayStatus: this.mapStatusToRobotState(robot.status)
      }));
    },
    (error) => {
      console.error('Error fetching data for zone:', error);
    }
  );
}

  startSignalRConnection(): void {
    this.hubConnection = new signalR.HubConnectionBuilder()
      .withUrl(`${environment.apis.default.url}/signalr-agv-status`)
      .configureLogging(signalR.LogLevel.Information)
      .build();

    this.hubConnection
      .start()
      .then(() => {
        console.log('SignalR Connected');
        this.hubConnection.on('ReceiveAgvStatus', () => {
          if (this.selectedZoneId) {
            this.fetchDataByZone(this.selectedZoneId);
          }
        });
      })
      .catch((err) => console.error('SignalR Connection Error:', err));
  }

   addZonesToMenu(): void {
    const andonMenu = this.routesService.find((route) => route.path === '/andon'); // Tìm menu Andon
    if (andonMenu) {
      this.zones.forEach((zone) => {
        this.routesService.add([
          {
            parentName: andonMenu.name, // Đặt menu con dưới menu Andon
            path: `/andon/${zone.id}`, // Đường dẫn động cho từng zone
            name: `${zone.zoneName}`, // Tên zone
            iconClass: 'fas fa-tv', // Icon cho menu con
            order: 10, // Thứ tự hiển thị
            layout: eLayoutType.application,
          },
        ]);
      });
    }
  }

  startTimer(): void {
    setInterval(() => {
      this.timer = new Date().toLocaleTimeString(); // Update timer every second
    }, 1000);
  }

  selectedAgvTask: AndonDetailDto | null = null;
  isAgvModalOpen = false;


  openAgvDetail(robot: AGVStatusDto): void {
    this.agvStatus.getStatusTaskByRobot(robot).subscribe({
      next: (result) => {
        if(result){
          this.selectedAgvTask = result;
        }
        this.isAgvModalOpen = true;
      }
    });
  }

  mapStatusToRobotState(status: string): 'Idle' | 'Running' | 'Charging' | 'Fault' | 'Stop' {
    const idleStatuses = ["1", "4"]; // Task completed, Idle
    const runningStatuses = ["2", "6", "8"]; // Executing, lifting, moving
    const chargingStatuses = ["7", "9"]; // Charging
    const stopStatuses = ["5"]; // Robot stopped
    const faultStatuses = [
      "3", "11", "12", "13", "14", "15", "16", "17", "18",
      "20", "21", "23", "24", "25", "26", "27", "28", "29",
      "30", "31", "33", "34"
    ]; // Tất cả lỗi/cảnh báo

    if (idleStatuses.includes(status)) return 'Idle';
    if (runningStatuses.includes(status)) return 'Running';
    if (chargingStatuses.includes(status)) return 'Charging';
    if (stopStatuses.includes(status)) return 'Stop';
    if (faultStatuses.includes(status)) return 'Fault';

    return 'Idle'; // fallback mặc định
  }


    getTaskStatusClass(status: string): string {
  switch (status) {
    case 'Created':
    case '1':
      return 'badge bg-secondary'; // xám
    case 'Success':
    case '9':
      return 'badge bg-success'; // xanh lá
    case '0':
    case '4':
    case '5':
      return 'badge bg-danger'; // đỏ
    default:
      return 'badge bg-warning text-dark'; // mặc định
  }
}

  getAgvStatusClass(status: string): string {
  switch (status) {
    case '3':
    case '4':
      return 'badge bg-secondary'; // xám
    case '1':
      return 'badge bg-success'; // xanh lá
    case '2':
    case '6':
    case '7':
    case '8':
      return 'badge bg-warning text-dark'; // đỏ
    default:
      return 'badge bg-danger'; // mặc định
  }
}
}