import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AndonComponent } from './andon.component';
import { authGuard, permissionGuard } from '@abp/ng.core';

const routes: Routes = [{ path: '', component: AndonComponent, canActivate: [authGuard, permissionGuard]}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AndonRoutingModule { }
