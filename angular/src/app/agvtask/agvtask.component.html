

<div class="card">
  <div class="card-header">
    <div class="row">
      <div class="col col-md-6">
        <h5 class="card-title">
          {{ '::Menu:AGVTask' | abpLocalization }}
        </h5>
      </div>
      <div class="text-end col col-md-6"></div>
    </div>
  </div>
  <div class="card-body">
    <form [formGroup]="filterForm" (ngSubmit)="search()" class="mb-3 row align-items-end">
      <div class="col-md-2">
        <label>{{ '::AGVType' | abpLocalization }}</label>
            <select class="form-control" formControlName="taskType">
      <option *ngFor="let status of statusTypeOptions" [ngValue]="status">
        {{ status === '' ? ('::All' | abpLocalization) : (status) | abpLocalization }}
      </option>
    </select>
      </div>
        <div class="col-md-2">
        <label>{{ '::AGVCode' | abpLocalization }}</label>
        <input type="text" class="form-control" formControlName="robotTaskCode" />
      </div>
        <div class="col-md-2">
    <label>{{ '::TaskStatus' | abpLocalization }}</label>
    <select class="form-control" formControlName="taskStatus">
      <option *ngFor="let status of statusOptions" [ngValue]="status">
        {{ status === '' ? ('::All' | abpLocalization) : ('::Enum:AgvTaskStatus.' + status) | abpLocalization }}
      </option>
    </select>
  </div>
      <div class="col-md-2">
        <label>{{ '::DateFrom' | abpLocalization }}</label>
        <input
            #datepicker1="ngbDatepicker"
            class="form-control"
            name="datepickerFrom"
            formControlName="dateFrom"
            ngbDatepicker
            (click)="datepicker1.toggle()"
          />
      </div>
        <div class="col-md-2">
        <label>{{ '::DateTo' | abpLocalization }}</label>
        <input
            #datepicker2="ngbDatepicker"
            class="form-control"
            name="datepickerTo"
            formControlName="dateTo"
            ngbDatepicker
            (click)="datepicker2.toggle()"
          />
      </div>
      <div class="col-md-2 text-end d-flex gap-2">
        <button class="btn btn-primary btn-sm w-auto" type="submit">
          <i class="fa fa-search me-1"></i>{{ '::Search' | abpLocalization }}
        </button>
        <button class="btn btn-secondary btn-sm w-auto" type="button" (click)="resetFilters()">
          <i class="fa fa-times me-1"></i>{{ '::Clear' | abpLocalization }}
        </button>
      </div>
    </form>
    <ngx-datatable [rows]="agvTask.items" [count]="agvTask.totalCount" [list]="list" default  [columnMode]="'force'">
      <ngx-datatable-column [name]="'::PDAOrderRecordID' | abpLocalization" [flexGrow]="1" prop="pdaOrderRecordID"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::AGVType' | abpLocalization" [flexGrow]="1" prop="taskType"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::AGVID' | abpLocalization" [flexGrow]="1" prop="agvid"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::Router' | abpLocalization" [flexGrow]="1" prop="router"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::PointOfFeeding' | abpLocalization" [flexGrow]="1" prop="targetRoute"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::Status' | abpLocalization" [flexGrow]="1" prop="taskStatus">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span [ngClass]="getTaskStatusClass(row.taskStatus)">
            {{ ('::Enum:AgvTaskStatus.' + row.taskStatus) | abpLocalization }}
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::TaskCreatedAt' | abpLocalization" [flexGrow]="1" prop="taskCreatedAt">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.taskCreatedAt | date: 'dd/MM/yyyy HH:mm:ss' }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::TaskCompletedAt' | abpLocalization" [flexGrow]="1" prop="taskCompletedAt">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.taskCompletedAt | date: 'dd/MM/yyyy HH:mm:ss' }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::ExecutionTime' | abpLocalization" [flexGrow]="1" prop="timeComplete"></ngx-datatable-column>
    </ngx-datatable>
  </div>
</div>
