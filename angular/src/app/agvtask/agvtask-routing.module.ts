import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AGVTaskComponent } from './agvtask.component';
import { authGuard, permissionGuard } from '@abp/ng.core';

const routes: Routes = [{ path: '', component: AGVTaskComponent, canActivate: [authGuard, permissionGuard]}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AGVTaskRoutingModule { }
