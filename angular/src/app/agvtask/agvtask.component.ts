import { ListService, PagedResultDto } from '@abp/ng.core';
import { Component, OnInit } from '@angular/core';
import { AGVTaskDto, AGVTaskService } from '../proxy/agvtasks';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { NgbDateNativeAdapter, NgbDateAdapter } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationService, Confirmation } from '@abp/ng.theme.shared';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-agvtask',
  templateUrl: './agvtask.component.html',
  styleUrl: './agvtask.component.scss',
  standalone: false,
  providers: [ListService, { provide: NgbDateAdapter, useClass: NgbDateNativeAdapter }],
})
export class AGVTaskComponent implements OnInit {
  agvTask = { items: [], totalCount: 0 } as PagedResultDto<AGVTaskDto>;
  selectedAGVTask = {} as AGVTaskDto; // declare selectedAGVTask
  form: FormGroup;
  filterForm: FormGroup;
    statusOptions = [
      '','0','1','2','3','4','5','6','9','10'
];

    statusTypeOptions = ['',
  'PALLET', 
  'TROLLEY',
  'BOX'
  ];
  isModalOpen = false;
  constructor(public readonly list: ListService, 
    private agvTaskService: AGVTaskService, 
    private fb: FormBuilder,
    private confirmation: ConfirmationService,
    private datePipe: DatePipe
  ) {

        // Khởi tạo form lọc
    const date = new Date();

    this.filterForm = this.fb.group({
      taskType: [''],
      robotTaskCode: [''],
      taskStatus: [''],
      dateFrom: [date],
      dateTo: [date]
    });

    // Kết nối dữ liệu với bộ lọc
    this.list.hookToQuery((query) => this.getData(query)).subscribe((res) => {
      this.agvTask = res;
    });
  }

  ngOnInit() {
    console.log('RouterDataComponent initialized');
  }

  getData(query) {
    const filter = this.filterForm.value;

    return this.agvTaskService.getList({
      ...query,
      taskType: filter.taskType || undefined,
      robotTaskCode: filter.robotTaskCode || undefined,
      taskStatus: filter.taskStatus || undefined,
      dateFrom: filter.dateFrom ? this.datePipe.transform(filter.dateFrom, 'yyyy-MM-dd') : null,
      dateTo: filter.dateTo ? this.datePipe.transform(filter.dateTo, 'yyyy-MM-dd') : null
    });
  }

  search() {
    this.list.get();
  }

  resetFilters() {
    this.filterForm.reset();
    this.list.get();
  }

  createAGVTask() {
    this.selectedAGVTask = {} as AGVTaskDto; // reset the selected agvTask
    this.buildForm();
    this.isModalOpen = true;
  }

  // Add editAGVTask method
  editAGVTask(id: string) {
    this.agvTaskService.get(id).subscribe((agvTask) => {
      this.selectedAGVTask = agvTask;
      this.buildForm();
      this.isModalOpen = true;
    });
  }

  buildForm() {
    this.form = this.fb.group({
      pdaOrderRecordID: [this.selectedAGVTask.pdaOrderRecordID || '', Validators.required],
      taskType: [this.selectedAGVTask.taskType || '', Validators.required],
      targetRoute: [this.selectedAGVTask.targetRoute || ''],
      initPriotiry: [this.selectedAGVTask.initPriotiry || '', Validators.required],
      robotTaskCode: [this.selectedAGVTask.robotTaskCode || '', Validators.required],
      agvid: [this.selectedAGVTask.agvid || '', Validators.required],
      taskStatus: [this.selectedAGVTask.taskStatus || '', Validators.required],
      taskCreatedAt: [
        this.selectedAGVTask.taskCreatedAt ? new Date(this.selectedAGVTask.taskCreatedAt) : null,
        Validators.required,
      ],
      taskCompletedAt: [
        this.selectedAGVTask.taskCompletedAt ? new Date(this.selectedAGVTask.taskCompletedAt) : null,
        Validators.required,
      ],
    });
  }

  // change the save method
  save() {
    if (this.form.invalid) {
      return;
    }

    const request = this.selectedAGVTask.id
      ? this.agvTaskService.update(this.selectedAGVTask.id, this.form.value)
      : this.agvTaskService.create(this.form.value);

    request.subscribe(() => {
      this.isModalOpen = false;
      this.form.reset();
      this.list.get();
    });
  }

  delete(id: string) {
    this.confirmation.warn('::AreYouSureToDelete', '::AreYouSure').subscribe((status) => {
    if (status === Confirmation.Status.confirm) {
      this.agvTaskService.delete(id).subscribe(() => this.list.get());
    }
  });
}

getTaskStatusClass(status: string): string {
  switch (status) {
    case 'Created':
    case '1':
      return 'badge bg-secondary'; // xám
    // case 'Executing':
    // case '2':
    //   return 'badge bg-warning text-dark'; // vàng
    // case 'Sending':
    // case '3':
    //   return 'badge bg-primary'; // xanh dương
    case 'Success':
    case '9':
      return 'badge bg-success'; // xanh lá
    case '0':
    case '4':
    case '5':
      return 'badge bg-danger'; // đỏ
    default:
      return 'badge bg-warning text-dark'; // mặc định
  }
}

}
