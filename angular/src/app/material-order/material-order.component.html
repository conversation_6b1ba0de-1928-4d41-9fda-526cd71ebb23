


<div class="card">
  <div class="card-header">
    <div class="row">
      <div class="col col-md-6">
        <h5 class="card-title">
          {{ '::Menu:MaterialOrder' | abpLocalization }}
        </h5>
      </div>
      <div class="text-end col col-md-6"></div>
    </div>
  </div>
  <div class="card-body">
    <form [formGroup]="filterForm" (ngSubmit)="search()" class="mb-3 row align-items-end">
  <div class="col-md-2">
    <label>{{ '::PartNumber' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="partNumber" />
  </div>
    <div class="col-md-2">
    <label>{{ '::OrderID' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="orderID" />
  </div>
    <div class="col-md-2">
    <label>{{ '::Status' | abpLocalization }}</label>
    <select class="form-control" formControlName="status">
      <option *ngFor="let status of statusOptions" [ngValue]="status">
        {{ status === '' ? ('::All' | abpLocalization) : ('::Enum:OrderStatus.' + status) | abpLocalization }}
      </option>
    </select>
  </div>
  <div class="col-md-2">
    <label>{{ '::DateFrom' | abpLocalization }}</label>
    <input
        #datepicker1="ngbDatepicker"
        class="form-control"
        name="datepickerFrom"
        formControlName="dateFrom"
        ngbDatepicker
        (click)="datepicker1.toggle()"
      />
  </div>
    <div class="col-md-2">
    <label>{{ '::DateTo' | abpLocalization }}</label>
    <input
        #datepicker2="ngbDatepicker"
        class="form-control"
        name="datepickerTo"
        formControlName="dateTo"
        ngbDatepicker
        (click)="datepicker2.toggle()"
      />
  </div>
  <div class="col-md-2 text-end d-flex gap-2">
    <button class="btn btn-primary btn-sm w-auto" type="submit">
      <i class="fa fa-search me-1"></i>{{ '::Search' | abpLocalization }}
    </button>
    <button class="btn btn-secondary btn-sm w-auto" type="button" (click)="resetFilters()">
      <i class="fa fa-times me-1"></i>{{ '::Clear' | abpLocalization }}
    </button>
  </div>
</form>
    <ngx-datatable [rows]="materialOrder.items" [count]="materialOrder.totalCount" [list]="list" default  [columnMode]="'force'">
      <ngx-datatable-column [name]="'::OrderID' | abpLocalization" [flexGrow]="1" prop="orderID"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::PartNumber' | abpLocalization" [flexGrow]="1" prop="partNumber"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::BatchNumber' | abpLocalization" [flexGrow]="1" prop="batchNumber"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::Quantity' | abpLocalization" [flexGrow]="1" prop="quantity"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::CapturedAt' | abpLocalization" [flexGrow]="1" prop="creationTime">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.creationTime | date: 'dd/MM/yyyy HH:mm:ss' }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::Status' | abpLocalization" [flexGrow]="1" prop="status">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span [ngClass]="getOrderStatusClass(row.status)">
            {{ ('::Enum:OrderStatus.' + row.status) | abpLocalization }}
          </span>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::DateComplated' | abpLocalization" [flexGrow]="1" prop="timeComplated">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.timeComplated | date: 'dd/MM/yyyy HH:mm:ss' }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::CompleteBy' | abpLocalization" [flexGrow]="1" prop="complatedBy">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <span *ngIf="row.complatedBy">
            {{ '::Enum:CompleteBy.' + row.complatedBy | abpLocalization }}
          </span>
        </ng-template>
      </ngx-datatable-column>

    </ngx-datatable>
  </div>
</div>