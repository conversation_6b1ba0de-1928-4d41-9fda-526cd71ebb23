import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MaterialOrderComponent } from './material-order.component';
import { authGuard, permissionGuard } from '@abp/ng.core';

const routes: Routes = [{ path: '', component: MaterialOrderComponent, canActivate: [authGuard, permissionGuard]}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MaterialOrderRoutingModule { }
