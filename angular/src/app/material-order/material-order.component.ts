import { ListService, PagedResultDto } from '@abp/ng.core';
import { Component, OnInit } from '@angular/core';
import { MaterialOrderDto, MaterialOrderService } from '../proxy/material-orders';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { NgbDateNativeAdapter, NgbDateAdapter } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationService, Confirmation } from '@abp/ng.theme.shared';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-material-order',
  templateUrl: './material-order.component.html',
  styleUrl: './material-order.component.scss',
  standalone: false,
  providers: [ListService, { provide: NgbDateAdapter, useClass: NgbDateNativeAdapter }],
})
export class MaterialOrderComponent implements OnInit {
  materialOrder = { items: [], totalCount: 0 } as PagedResultDto<MaterialOrderDto>;
  selectedMaterialOrder = {} as MaterialOrderDto; // declare selectedMaterialOrder
  form: FormGroup;
    filterForm: FormGroup;
  statusOptions = [
    '','0', '1', '2'
  ];
  isModalOpen = false;
  constructor(public readonly list: ListService, 
    private materialOrderService: MaterialOrderService, 
    private fb: FormBuilder,
    private confirmation: ConfirmationService,
        private datePipe: DatePipe
  ) {

    // Khởi tạo form lọc

    // Khởi tạo form lọc
    const date = new Date();
    this.filterForm = this.fb.group({
      partNumber: [''],
      orderID: [''],
      status: [''],
      dateFrom: [date],
      dateTo: [date]
    });

    // Kết nối dữ liệu với bộ lọc
    this.list.hookToQuery((query) => this.getData(query)).subscribe((res) => {
      this.materialOrder = res;
    });
  }

  ngOnInit() {
    console.log('RouterDataComponent initialized');
    console.log(this.statusOptions);
  }

    getData(query) {
    const filter = this.filterForm.value;

    return this.materialOrderService.getList({
      ...query,
      partNumber: filter.partNumber || undefined,
      orderID: filter.orderID || undefined,
      status: filter.status || undefined,
      dateFrom: filter.dateFrom ? this.datePipe.transform(filter.dateFrom, 'yyyy-MM-dd') : null,
      dateTo: filter.dateTo ? this.datePipe.transform(filter.dateTo, 'yyyy-MM-dd') : null
    });
  }

  search() {
    this.list.get();
  }

  resetFilters() {
    this.filterForm.reset();
    this.list.get();
  }

  getOrderStatusClass(status: string): string {
  switch (status) {
    case 'Created':
    case '2':
      return 'badge bg-secondary'; // xám
    // case 'Executing':
    // case '2':
    //   return 'badge bg-warning text-dark'; // vàng
    // case 'Sending':
    // case '3':
    //   return 'badge bg-primary'; // xanh dương
    case 'Success':
    case '1':
      return 'badge bg-success'; // xanh lá
    case '0':
    case '4':
    case '5':
      return 'badge bg-danger'; // đỏ
    default:
      return 'badge bg-warning text-dark'; // mặc định
  }
}
}
