import { ListService, PagedResultDto } from '@abp/ng.core';
import { Component, OnInit } from '@angular/core';
import { ZoneDto, ZoneService } from '../proxy/zones';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { NgbDateNativeAdapter, NgbDateAdapter } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationService, Confirmation } from '@abp/ng.theme.shared';

@Component({
  selector: 'app-zone',
  templateUrl: './zone.component.html',
  styleUrl: './zone.component.scss',
  standalone: false,
  providers: [ListService],
})
export class ZoneComponent implements OnInit {
  zone = { items: [], totalCount: 0 } as PagedResultDto<ZoneDto>;
  selectedZone = {} as ZoneDto; // declare selectedZone
  form: FormGroup;
    filterForm: FormGroup;
  //zoneTypes = zoneTypeOptions;
  isModalOpen = false;
  constructor(public readonly list: ListService, 
    private zoneService: ZoneService, 
    private fb: FormBuilder,
    private confirmation: ConfirmationService
  ) {

    // Khởi tạo form lọc
    this.filterForm = this.fb.group({
      zoneName: [''],
    });

    // Kết nối dữ liệu với bộ lọc
    this.list.hookToQuery((query) => this.getData(query)).subscribe((res) => {
      this.zone = res;
    });
  }

  ngOnInit() {
    console.log('RouterDataComponent initialized');
  }

  getData(query) {
    const filter = this.filterForm.value;

    return this.zoneService.getList({
      ...query,
      zoneName: filter.zoneName || undefined,
    });
  }

  search() {
    this.list.get();
  }

  resetFilters() {
    this.filterForm.reset();
    this.list.get();
  }

  createZone() {
    this.selectedZone = {} as ZoneDto; // reset the selected zone
    this.buildForm();
    this.isModalOpen = true;
  }

  // Add editZone method
  editZone(id: string) {
    this.zoneService.get(id).subscribe((zone) => {
      this.selectedZone = zone;
      this.buildForm();
      this.isModalOpen = true;
    });
  }

  buildForm() {
    this.form = this.fb.group({
      zoneName: [this.selectedZone.zoneName || '', Validators.required],
      xmin: [this.selectedZone.xmin || '', Validators.required],
      ymin: [this.selectedZone.ymin || '', Validators.required],
      xmax: [this.selectedZone.xmax || '', Validators.required],
      ymax: [this.selectedZone.ymax || '', Validators.required],
    });
  }

  // change the save method
  save() {
    if (this.form.invalid) {
      return;
    }

    const request = this.selectedZone.id
      ? this.zoneService.update(this.selectedZone.id, this.form.value)
      : this.zoneService.create(this.form.value);

    request.subscribe(() => {
      this.isModalOpen = false;
      this.form.reset();
      this.list.get();
    });
  }

  deleteZone(id: string) {
    this.confirmation.warn('::AreYouSureToDelete', '::AreYouSure').subscribe((status) => {
    if (status === Confirmation.Status.confirm) {
      this.zoneService.delete(id).subscribe(() => this.list.get());
    }
  });
}
}
