

<div class="card">
  <div class="card-header">
    <div class="row">
      <div class="col col-md-6">
        <h5 class="card-title">{{ '::Menu:Zone' | abpLocalization }}</h5>
      </div>
      <div class="text-end col col-md-6 text-lg-end pt-2">
        <button *abpPermission="'SOR.Zones.Create'" id="create" class="btn btn-primary" type="button" (click)="createZone()">
          <i class="fa fa-plus me-1"></i>
          <span>{{ "::Create" | abpLocalization }}</span>
        </button>
      </div>
    </div>
  </div>

  <div class="card-body">
    
<form [formGroup]="filterForm" (ngSubmit)="search()" class="mb-3 row align-items-end">
  <div class="col-md-3">
    <label>{{ '::ZoneName' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="zoneName" />
  </div>

  <div class="col-md-7"></div>
  <div class="col-md-2 text-end d-flex gap-2">
    <button class="btn btn-primary btn-sm w-auto" type="submit">
      <i class="fa fa-search me-1"></i>{{ '::Search' | abpLocalization }}
    </button>
    <button class="btn btn-secondary btn-sm w-auto" type="button" (click)="resetFilters()">
      <i class="fa fa-times me-1"></i>{{ '::Clear' | abpLocalization }}
    </button>
  </div>
</form>
    <ngx-datatable
      [rows]="zone.items"
      [count]="zone.totalCount"
      [list]="list" default
       [columnMode]="'force'">

      <!-- Actions Column -->
      <ngx-datatable-column
        [name]="'::Actions' | abpLocalization"
        [maxWidth]="150"
        [sortable]="false"
      >
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div ngbDropdown container="body" class="d-inline-block">
            <button class="btn btn-primary btn-sm dropdown-toggle" ngbDropdownToggle>
              <i class="fa fa-cog me-1"></i>{{ '::Actions' | abpLocalization }}
            </button>
            <div ngbDropdownMenu>
              <button *abpPermission="'SOR.Zones.Update'" ngbDropdownItem (click)="editZone(row.id)">
                {{ '::Edit' | abpLocalization }}
              </button>
              <button *abpPermission="'SOR.Zones.Delete'" ngbDropdownItem (click)="deleteZone(row.id)">
                {{ '::Delete' | abpLocalization }}
              </button>
            </div>
          </div>
        </ng-template>
      </ngx-datatable-column>

      <ngx-datatable-column [name]="'::ZoneName' | abpLocalization" [flexGrow]="1" prop="zoneName"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::Xmin' | abpLocalization" [flexGrow]="1" prop="xmin">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.xmin }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::Ymin' | abpLocalization" [flexGrow]="1" prop="ymin">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.ymin  }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::Xmax' | abpLocalization" [flexGrow]="1" prop="xmax">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.xmax  }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::Ymax' | abpLocalization" [flexGrow]="1" prop="ymax">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.ymax }}
        </ng-template>
      </ngx-datatable-column>


    </ngx-datatable>

  </div>
</div>

<!-- Modal -->
<abp-modal [(visible)]="isModalOpen">
  <ng-template #abpHeader>
    <h3>{{ (selectedZone?.id ? '::Permission:Zones.Update' : '::Permission:Zones.Create') | abpLocalization }}</h3>
  </ng-template>

    <ng-template #abpBody>
      <form [formGroup]="form" (ngSubmit)="save()">
        <div class="mt-2">
          <label for="zone-name">{{'::ZoneName' | abpLocalization}}</label><span> * </span>
          <input
            type="text"
            id="zone-name"
            class="form-control"
            formControlName="zoneName"
            autofocus
          />
        </div>

        <div class="mt-2">
          <label for="xmin">{{'::Xmin' | abpLocalization}}</label><span> * </span>
          <input
            type="number"
            id="xmin"
            class="form-control"
            formControlName="xmin"
          />
        </div>

                <div class="mt-2">
          <label for="ymin">{{'::Ymin' | abpLocalization}}</label><span> * </span>
          <input
            type="number"
            id="ymin"
            class="form-control"
            formControlName="ymin"
          />
        </div>

                <div class="mt-2">
          <label for="xmax">{{'::Xmax' | abpLocalization}}</label><span> * </span>
          <input
            type="number"
            id="xmax"
            class="form-control"
            formControlName="xmax"
          />
        </div>

                <div class="mt-2">
          <label for="ymax">{{'::Ymax' | abpLocalization}}</label><span> * </span>
          <input
            type="number"
            id="ymax"
            class="form-control"
            formControlName="ymax"
          />
        </div>
        
      </form>
    </ng-template>


  <ng-template #abpFooter>
    <button type="button" class="btn btn-secondary" abpClose>
      {{ '::Close' | abpLocalization }}
    </button>
    <button class="btn btn-primary" (click)="save()" [disabled]="form.invalid">
      <i class="fa fa-check mr-1"></i>
      {{ '::Save' | abpLocalization }}
    </button>
  </ng-template>
</abp-modal>