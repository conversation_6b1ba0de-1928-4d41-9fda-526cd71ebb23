import { ListService, PagedResultDto } from '@abp/ng.core';
import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { MaterialMasterDto, MaterialMasterService } from '../proxy/material-masters';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { NgbDateNativeAdapter, NgbDateAdapter } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationService, Confirmation } from '@abp/ng.theme.shared';
import { ToasterService } from '@abp/ng.theme.shared';

@Component({
  selector: 'app-material-master',
  templateUrl: './material-master.component.html',
  styleUrl: './material-master.component.scss',
  standalone: false,
  providers: [ListService],
})
export class MaterialMasterComponent implements OnInit {
  materialMaster = { items: [], totalCount: 0 } as PagedResultDto<MaterialMasterDto>;
  selectedMaterialMaster = {} as MaterialMasterDto; // declare selectedMaterialMaster
  form: FormGroup;
    filterForm: FormGroup;
  //materialMasterTypes = materialMasterTypeOptions;
  isModalOpen = false;
  constructor(public readonly list: ListService, 
    private materialMasterService: MaterialMasterService, 
    private fb: FormBuilder,
    private confirmation: ConfirmationService,
    private toaster: ToasterService
  ) {

    // Khởi tạo form lọc
    this.filterForm = this.fb.group({
      partNumber: [''],
      defaultPOF: [''],
    });

    // Kết nối dữ liệu với bộ lọc
    this.list.hookToQuery((query) => this.getData(query)).subscribe((res) => {
      this.materialMaster = res;
    });
  }

  ngOnInit() {
    console.log('RouterDataComponent initialized');
  }

  getData(query) {
    const filter = this.filterForm.value;

    return this.materialMasterService.getList({
      ...query,
      partNumber: filter.partNumber || undefined,
      defaultPOF: filter.defaultPOF || undefined,
    });
  }

  search() {
    this.list.get();
  }

  resetFilters() {
    this.filterForm.reset();
    this.list.get();
  }

  createMaterialMaster() {
    this.selectedMaterialMaster = {} as MaterialMasterDto; // reset the selected materialMaster
    this.buildForm();
    this.isModalOpen = true;
  }

  // Add editMaterialMaster method
  editMaterialMaster(id: string) {
    this.materialMasterService.get(id).subscribe((materialMaster) => {
      this.selectedMaterialMaster = materialMaster;
      this.buildForm();
      this.isModalOpen = true;
    });
  }

  buildForm() {
    this.form = this.fb.group({
      partNumber: [this.selectedMaterialMaster.partNumber || '', Validators.required],
      partName: [this.selectedMaterialMaster.partName || '', Validators.required],
      description: [this.selectedMaterialMaster.description || ''],
      defaultPOF: [this.selectedMaterialMaster.defaultPOF || '', Validators.required],
      // type: [this.selectedMaterialMaster.type || null, Validators.required],
      // publishDate: [
      //   this.selectedMaterialMaster.publishDate ? new Date(this.selectedMaterialMaster.publishDate) : null,
      //   Validators.required,
      // ],
      // price: [this.selectedMaterialMaster.price || null, Validators.required],
    });
  }

  // change the save method
  save() {
    if (this.form.invalid) {
      return;
    }

    const request = this.selectedMaterialMaster.id
      ? this.materialMasterService.update(this.selectedMaterialMaster.id, this.form.value)
      : this.materialMasterService.create(this.form.value);

    request.subscribe(() => {
      this.isModalOpen = false;
      this.form.reset();
      this.list.get();
    });
  }

  delete(id: string) {
    this.confirmation.warn('::AreYouSureToDelete', '::AreYouSure').subscribe((status) => {
    if (status === Confirmation.Status.confirm) {
      this.materialMasterService.delete(id).subscribe(() => this.list.get());
    }
    });
  }

  selectedFile?: File;

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input?.files?.length) {
    this.selectedFile = input.files[0];
    }
  }

  onUpload() {
    if (!this.selectedFile) return;
    const formData = new FormData();
    formData.append('file', this.selectedFile);

    this.materialMasterService.upload(formData).subscribe({
      next: () => {
        this.toaster.success('SOR::SaveSuccess', 'SOR::Success');
        this.list.get();
      },
      error: (err) => {
        this.toaster.error('SOR::SaveError', 'SOR::Error');
      }
    });
  }

    downloadSample() {
  this.materialMasterService.download().subscribe({
    next: (blob) => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'sample-material-master.xlsx';
      a.click();
      window.URL.revokeObjectURL(url);
    },
    error: (err) => {
      console.error('Lỗi khi tải file mẫu:', err);
    }
  });
}
}
