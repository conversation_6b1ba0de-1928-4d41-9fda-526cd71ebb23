


<div class="card">
  <div class="card-header router-header">
    <div class="row">
      <div class="col col-md-6">
        <h5 class="card-title">
          {{ '::Menu:MaterialMaster' | abpLocalization }}
        </h5>
      </div>
      <div class="col-md-6 text-end d-flex justify-content-end gap-2 flex-wrap pt-2">
                <a class="btn btn-outline-secondary btn-sm d-flex align-items-center" (click)="downloadSample()">
          <i class="fa fa-download me-1"></i> Tải mẫu
        </a>
        <form (ngSubmit)="onUpload()" #uploadForm="ngForm" class="d-flex align-items-center gap-2 upload-form">
          <input type="file" class="form-control form-control-sm" (change)="onFileSelected($event)" accept=".xlsx" required />
          <button class="btn btn-secondary btn-sm" type="submit" [disabled]="!selectedFile">
            <i class="fa fa-upload me-1"></i> Import
          </button>
        </form>
      </div>
    </div>
  </div>
  <div class="card-body">
    <form [formGroup]="filterForm" (ngSubmit)="search()" class="mb-3 row align-items-end">
  <div class="col-md-3">
    <label>{{ '::PartNumber' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="partNumber" />
  </div>
    <div class="col-md-3">
    <label>{{ '::DefaultPOF' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="defaultPOF" />
  </div>
  <div class="col-md-4"></div>
  <div class="col-md-2 text-end d-flex gap-2">
    <button class="btn btn-primary btn-sm w-auto" type="submit">
      <i class="fa fa-search me-1"></i>{{ '::Search' | abpLocalization }}
    </button>
    <button class="btn btn-secondary btn-sm w-auto" type="button" (click)="resetFilters()">
      <i class="fa fa-times me-1"></i>{{ '::Clear' | abpLocalization }}
    </button>
  </div>
</form>
    <ngx-datatable [rows]="materialMaster.items" [count]="materialMaster.totalCount" [list]="list" default [columnMode]="'force'">
      <ngx-datatable-column [name]="'::PartNumber' | abpLocalization" [flexGrow]="1" prop="partNumber"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::PartName' | abpLocalization" [flexGrow]="1" prop="partName"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::Description' | abpLocalization" [flexGrow]="1" prop="description"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::DefaultPOF' | abpLocalization" [flexGrow]="1" prop="defaultPOF"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::CreationTime' | abpLocalization" prop="creationTime">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.creationTime | date: 'dd/MM/yyyy HH:mm:ss' }}
        </ng-template>
      </ngx-datatable-column>
            <ngx-datatable-column [name]="'::LastModificationTime' | abpLocalization" prop="lastModificationTime">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.lastModificationTime | date: 'dd/MM/yyyy HH:mm:ss' }}
        </ng-template>
      </ngx-datatable-column>
    </ngx-datatable>
  </div>
</div>
