import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AGVTaskTempComponent } from './agvtask-temp.component';
import { authGuard, permissionGuard } from '@abp/ng.core';

const routes: Routes = [{ path: '', component: AGVTaskTempComponent, canActivate: [authGuard, permissionGuard]}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AGVTaskTempRoutingModule { }
