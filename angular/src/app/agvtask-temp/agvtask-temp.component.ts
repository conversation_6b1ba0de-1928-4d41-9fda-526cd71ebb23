import { ListService, PagedResultDto } from '@abp/ng.core';
import { Component, OnInit } from '@angular/core';
import { AGVTaskTempDto, AGVTaskTempService } from '../proxy/agvtask-temps';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { NgbDateNativeAdapter, NgbDateAdapter } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationService, Confirmation } from '@abp/ng.theme.shared';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-agvtask-temp',
  templateUrl: './agvtask-temp.component.html',
  styleUrl: './agvtask-temp.component.scss',
  standalone: false,
  providers: [ListService],
})
export class AGVTaskTempComponent implements OnInit {
  agvTaskTemp = { items: [], totalCount: 0 } as PagedResultDto<AGVTaskTempDto>;
  selectedAGVTaskTemp = {} as AGVTaskTempDto; // declare selectedAGVTaskTemp
  form: FormGroup;
    filterForm: FormGroup;
  //agvTaskTempTypes = agvTaskTempTypeOptions;
  isModalOpen = false;
  constructor(public readonly list: ListService, 
    private agvTaskTempTempService: AGVTaskTempService, 
    private fb: FormBuilder,
    private confirmation: ConfirmationService,
    private datePipe: DatePipe
  ) {

            // Khởi tạo form lọc
    const date = new Date();

    this.filterForm = this.fb.group({
      taskType: [''],
      robotTaskCode: [''],
      taskStatus: [''],
      dateFrom: [date],
      dateTo: [date]
    });

    // Kết nối dữ liệu với bộ lọc
    this.list.hookToQuery((query) => this.getData(query)).subscribe((res) => {
      this.agvTaskTemp = res;
    });
  }

  ngOnInit() {
    console.log('RouterDataComponent initialized');
  }

  getData(query) {
    const filter = this.filterForm.value;

    return this.agvTaskTempTempService.getList({
      ...query,
      taskType: filter.taskType || undefined,
      robotTaskCode: filter.taskStatus || undefined,
      taskStatus: filter.taskStatus || undefined,
      dateFrom: filter.dateFrom ? this.datePipe.transform(filter.dateFrom, 'yyyy-MM-dd') : null,
      dateTo: filter.dateTo ? this.datePipe.transform(filter.dateTo, 'yyyy-MM-dd') : null
    });
  }

  search() {
    this.list.get();
  }

  resetFilters() {
    this.filterForm.reset();
    this.list.get();
  }

  createAGVTaskTemp() {
    this.selectedAGVTaskTemp = {} as AGVTaskTempDto; // reset the selected agvTaskTemp
    this.buildForm();
    this.isModalOpen = true;
  }

  // Add editAGVTaskTemp method
  editAGVTaskTemp(id: string) {
    this.agvTaskTempTempService.get(id).subscribe((agvTaskTemp) => {
      this.selectedAGVTaskTemp = agvTaskTemp;
      this.buildForm();
      this.isModalOpen = true;
    });
  }

  buildForm() {
    this.form = this.fb.group({
      pdaOrderRecordID: [this.selectedAGVTaskTemp.pdaOrderRecordID || '', Validators.required],
      taskType: [this.selectedAGVTaskTemp.taskType || '', Validators.required],
      targetRoute: [this.selectedAGVTaskTemp.targetRoute || ''],
      initPriotiry: [this.selectedAGVTaskTemp.initPriotiry || '', Validators.required],
      robotTaskCode: [this.selectedAGVTaskTemp.robotTaskCode || '', Validators.required],
      agvid: [this.selectedAGVTaskTemp.agvid || '', Validators.required],
      taskStatus: [this.selectedAGVTaskTemp.taskStatus || '', Validators.required],
      taskCreatedAt: [
        this.selectedAGVTaskTemp.taskCreatedAt ? new Date(this.selectedAGVTaskTemp.taskCreatedAt) : null,
        Validators.required,
      ],
      taskCompletedAt: [
        this.selectedAGVTaskTemp.taskCompletedAt ? new Date(this.selectedAGVTaskTemp.taskCompletedAt) : null,
        Validators.required,
      ],
    });
  }

  // change the save method
  save() {
    if (this.form.invalid) {
      return;
    }

    const request = this.selectedAGVTaskTemp.id
      ? this.agvTaskTempTempService.update(this.selectedAGVTaskTemp.id, this.form.value)
      : this.agvTaskTempTempService.create(this.form.value);

    request.subscribe(() => {
      this.isModalOpen = false;
      this.form.reset();
      this.list.get();
    });
  }

  delete(id: string) {
    this.confirmation.warn('::AreYouSureToDelete', '::AreYouSure').subscribe((status) => {
    if (status === Confirmation.Status.confirm) {
      this.agvTaskTempTempService.delete(id).subscribe(() => this.list.get());
    }
  });
}
}
