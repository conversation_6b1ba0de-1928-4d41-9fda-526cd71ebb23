import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AGVTaskTempRoutingModule } from './agvtask-temp-routing.module';
import { AGVTaskTempComponent } from './agvtask-temp.component';
import { SharedModule } from '../shared/shared.module';
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';


@NgModule({
  declarations: [
    AGVTaskTempComponent,
  ],
  imports: [
    CommonModule,
    AGVTaskTempRoutingModule,
    SharedModule,
    NgbDatepickerModule
  ]
})
export class AGVTaskTempModule { }
