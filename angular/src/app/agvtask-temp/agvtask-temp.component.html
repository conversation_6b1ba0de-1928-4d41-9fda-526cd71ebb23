


<div class="card">
  <div class="card-header">
    <div class="row">
      <div class="col col-md-6">
        <h5 class="card-title">
          {{ '::Menu:AGVTaskTemp' | abpLocalization }}
        </h5>
      </div>
      <div class="text-end col col-md-6"></div>
    </div>
  </div>
  <div class="card-body">
    <form [formGroup]="filterForm" (ngSubmit)="search()" class="mb-3 row align-items-end">
  <div class="col-md-4">
    <label>{{ '::TaskType' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="taskType" />
  </div>
    <div class="col-md-4">
    <label>{{ '::RobotTaskCode' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="robotTaskCode" />
  </div>
    <div class="col-md-4">
    <label>{{ '::TaskStatus' | abpLocalization }}</label>
    <input type="text" class="form-control" formControlName="taskStatus" />
  </div>
  <div class="col-md-4">
    <label>{{ '::DateFrom' | abpLocalization }}</label>
    <input
        #datepicker1="ngbDatepicker"
        class="form-control"
        name="datepickerFrom"
        formControlName="dateFrom"
        ngbDatepicker
        (click)="datepicker1.toggle()"
      />
  </div>
    <div class="col-md-4">
    <label>{{ '::DateTo' | abpLocalization }}</label>
    <input
        #datepicker2="ngbDatepicker"
        class="form-control"
        name="datepickerTo"
        formControlName="dateTo"
        ngbDatepicker
        (click)="datepicker2.toggle()"
      />
  </div>
  <div class="col-md-2"></div>
  <div class="col-md-2 text-end d-flex gap-2">
    <button class="btn btn-primary btn-sm w-auto" type="submit">
      <i class="fa fa-search me-1"></i>{{ '::Search' | abpLocalization }}
    </button>
    <button class="btn btn-secondary btn-sm w-auto" type="button" (click)="resetFilters()">
      <i class="fa fa-times me-1"></i>{{ '::Clear' | abpLocalization }}
    </button>
  </div>
</form>
    <ngx-datatable [rows]="agvTaskTemp.items" [count]="agvTaskTemp.totalCount" [list]="list" default  [columnMode]="'force'">
      <ngx-datatable-column [name]="'::PDAOrderRecordID' | abpLocalization" [flexGrow]="1" prop="pdaOrderRecordID"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::TaskType' | abpLocalization" [flexGrow]="1" prop="taskType"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::TargetRoute' | abpLocalization" [flexGrow]="1" prop="targetRoute"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::InitPriotiry' | abpLocalization" [flexGrow]="1" prop="initPriotiry"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::RobotTaskCode' | abpLocalization" [flexGrow]="1" prop="robotTaskCode"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::AGVID' | abpLocalization" [flexGrow]="1" prop="agvid"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::TaskStatus' | abpLocalization" [flexGrow]="1" prop="taskStatus"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::TaskCreatedAt' | abpLocalization" [flexGrow]="1" prop="taskCreatedAt">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.taskCreatedAt | date: 'dd/MM/yyyy HH:mm:ss' }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::TaskCompletedAt' | abpLocalization" prop="taskCompletedAt">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.taskCompletedAt | date: 'dd/MM/yyyy HH:mm:ss' }}
        </ng-template>
      </ngx-datatable-column>
    </ngx-datatable>
  </div>
</div>
