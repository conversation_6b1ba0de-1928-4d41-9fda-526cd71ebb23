import { RoutesService, eLayoutType } from '@abp/ng.core';
import { inject, provideAppInitializer } from '@angular/core';

export const APP_ROUTE_PROVIDER = [
  provideAppInitializer(() => {
    configureRoutes();
  }),
];

function configureRoutes() {
  const routes = inject(RoutesService);
  routes.add([
      // {
      //   path: '/',
      //   name: '::Menu:Home',
      //   iconClass: 'fas fa-home',
      //   order: 1,
      //   layout: eLayoutType.application,
      // },
      {
        path: '/agv-task-temps',
        name: '::Menu:AGVTaskTemp',
        iconClass: 'fas fa-clock',
        order: 1,
        layout: eLayoutType.application,
        requiredPolicy: 'AGVTaskTemp.Default'
      },
            {
        path: '/andon',
        name: '::Menu:Andon',
        iconClass: 'fas fa-chart-line', 
        order: 2,
        layout: eLayoutType.application,
      },
      {
        path: '/statistic',
        name: '::Menu:Statistic',
        iconClass: 'fas fa-chart-bar',
        order: 3,
        layout: eLayoutType.application,
        requiredPolicy: 'Statistic.Default'
      },
      {
        path: '/agv-tasks',
        name: '::Menu:AGVTask',
        iconClass: 'fas fa-tasks',
        order: 4,
        layout: eLayoutType.application,
        requiredPolicy: 'SOR.AGVTasks'
      },
      {
        path: '/pda-order-records',
        name: '::Menu:PDAOrderRecord',
        iconClass: 'fas fa-mobile-alt',
        order: 5,
        layout: eLayoutType.application,
        requiredPolicy: 'SOR.PDAOrderRecords'
      },
      {
        path: '/material-orders',
        name: '::Menu:MaterialOrder',
        iconClass: 'fas fa-clipboard-list',
        order: 6,
        layout: eLayoutType.application,
        requiredPolicy: 'SOR.MaterialOrders'
      },
      {
        path: '/material-masters',
        name: '::Menu:MaterialMaster',
        iconClass: 'fas fa-boxes',
        order: 7,
        layout: eLayoutType.application,
        requiredPolicy: 'SOR.MaterialMasters'
      },
      {
        path: '/agvs',
        name: '::Menu:AGV',
        iconClass: 'fas fa-robot',
        order: 8,
        layout: eLayoutType.application,
        requiredPolicy: 'SOR.AGVs'
      },
      {
        path: '/router-datas',
        name: '::Menu:RouterData',
        iconClass: 'fas fa-database',
        order: 9,
        layout: eLayoutType.application,
        requiredPolicy: 'SOR.RouterDatas'
      },
      {
        path: '/zone',
        name: '::Menu:Zone',
        iconClass: 'fas fa-map-marked-alt',
        order: 10,
        layout: eLayoutType.application,
        requiredPolicy: 'SOR.Zones'
      },
            {
        path: '/flag',
        name: '::Menu:Flag',
        iconClass: 'fas fa-flag',
        order: 11,
        layout: eLayoutType.application,
        requiredPolicy: 'SOR.Flags'
      },
            {
        path: '/agv-status-logs',
        name: '::Menu:ErrorHandling',
        iconClass: 'fas fa-bug', // Icon phù hợp cho Andon
        order: 12,
        layout: eLayoutType.application,
        requiredPolicy: 'SOR.ErrorHandlings'
      },
  ]);
}
