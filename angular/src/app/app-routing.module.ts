import { authGuard, permissionGuard } from '@abp/ng.core';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AndonComponent } from './andon/andon.component';

const routes: Routes = [
  // {
  //   path: '',
  //   pathMatch: 'full',
  //   loadChildren: () => import('./home/<USER>').then(m => m.HomeModule),
  // },
  {
    path: 'account',
    loadChildren: () => import('@abp/ng.account').then(m => m.AccountModule.forLazy()),
  },
  {
    path: 'identity',
    loadChildren: () => import('@abp/ng.identity').then(m => m.IdentityModule.forLazy()),
  },
  {
    path: 'setting-management',
    loadChildren: () =>
      import('@abp/ng.setting-management').then(m => m.SettingManagementModule.forLazy()),
  },
  { path: 'material-masters', loadChildren: () => import('./material-master/material-master.module').then(m => m.MaterialMasterModule) },
  { path: 'material-orders', loadChildren: () => import('./material-order/material-order.module').then(m => m.MaterialOrderModule) },
  { path: 'pda-order-records', loadChildren: () => import('./pdaorder-record/pdaorder-record.module').then(m => m.PDAOrderRecordModule) },
  { path: 'agvs', loadChildren: () => import('./agv/agv.module').then(m => m.AGVModule) },
  { path: 'agv-tasks', loadChildren: () => import('./agvtask/agvtask.module').then(m => m.AGVTaskModule) },
  { path: 'agv-task-temps', loadChildren: () => import('./view-task/view-task.module').then(m => m.ViewTaskModule) },
  { path: 'agv-status-logs', loadChildren: () => import('./error-handling/error-handling.module').then(m => m.ErrorHandlingModule) },
  { path: 'router-datas', loadChildren: () => import('./router-data/router-data.module').then(m => m.RouterDataModule) },
    /*...existing routes...*/
 // { path: 'andon', loadChildren: () => import('./andon/andon.module').then(m => m.AndonRoutingModule) },
  { path: 'andon', component: AndonComponent }, // Route chính cho Andon
  { path: 'andon/:zoneId', component: AndonComponent }, // Route động cho từng zone
  { path: '', redirectTo: '/andon', pathMatch: 'full' }, // Redirect mặc định
  { path: 'zone', loadChildren: () => import('./zone/zone.module').then(m => m.ZoneModule) },
  { path: 'statistic', loadChildren: () => import('./statistic/statistic.module').then(m => m.StatisticModule) },
  { path: 'flag', loadChildren: () => import('./flag/flag.module').then(m => m.FlagModule) },

];

@NgModule({
  imports: [RouterModule.forRoot(routes, {})],
  exports: [RouterModule],
})
export class AppRoutingModule {}
