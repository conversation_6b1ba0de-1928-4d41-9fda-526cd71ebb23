import { ListService, PagedResultDto } from '@abp/ng.core';
import { Component, OnInit } from '@angular/core';
import { AGVStatusLogDto, AGVStatusLogService } from '../proxy/agvstatus-logs';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { NgbDateNativeAdapter, NgbDateAdapter } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationService, Confirmation } from '@abp/ng.theme.shared';

@Component({
  selector: 'app-agvstatus-log',
  templateUrl: './agvstatus-log.component.html',
  styleUrl: './agvstatus-log.component.scss',
  standalone: false,
  providers: [ListService],
})
export class AGVStatusLogComponent implements OnInit {
  agvStatusLog = { items: [], totalCount: 0 } as PagedResultDto<AGVStatusLogDto>;
  selectedAGVStatusLog = {} as AGVStatusLogDto; // declare selectedAGVStatusLog
  form: FormGroup;
  //agvStatusLogTypes = agvStatusLogTypeOptions;
  isModalOpen = false;
  constructor(public readonly list: ListService, 
    private agvStatusLogService: AGVStatusLogService, 
    private fb: FormBuilder,
    private confirmation: ConfirmationService
  ) {}

  ngOnInit() {
    const agvStatusLogStreamCreator = (query) => this.agvStatusLogService.getList(query);

    this.list.hookToQuery(agvStatusLogStreamCreator).subscribe((response) => {
      this.agvStatusLog = response;
    });
  }

  createAGVStatusLog() {
    this.selectedAGVStatusLog = {} as AGVStatusLogDto; // reset the selected agvStatusLog
    this.buildForm();
    this.isModalOpen = true;
  }

  // Add editAGVStatusLog method
  editAGVStatusLog(id: string) {
    this.agvStatusLogService.get(id).subscribe((agvStatusLog) => {
      this.selectedAGVStatusLog = agvStatusLog;
      this.buildForm();
      this.isModalOpen = true;
    });
  }

  buildForm() {
    this.form = this.fb.group({
      agvid: [this.selectedAGVStatusLog.agvid || '', Validators.required],
      batteryLevel: [this.selectedAGVStatusLog.batteryLevel || '', Validators.required],
      operationStatus: [this.selectedAGVStatusLog.operationStatus || ''],
      errorMessage: [this.selectedAGVStatusLog.errorMessage || '', Validators.required],
      // type: [this.selectedAGVStatusLog.type || null, Validators.required],
      // publishDate: [
      //   this.selectedAGVStatusLog.publishDate ? new Date(this.selectedAGVStatusLog.publishDate) : null,
      //   Validators.required,
      // ],
      // price: [this.selectedAGVStatusLog.price || null, Validators.required],
    });
  }

  // change the save method
  save() {
    if (this.form.invalid) {
      return;
    }

    const request = this.selectedAGVStatusLog.id
      ? this.agvStatusLogService.update(this.selectedAGVStatusLog.id, this.form.value)
      : this.agvStatusLogService.create(this.form.value);

    request.subscribe(() => {
      this.isModalOpen = false;
      this.form.reset();
      this.list.get();
    });
  }

  delete(id: string) {
    this.confirmation.warn('::AreYouSureToDelete', '::AreYouSure').subscribe((status) => {
    if (status === Confirmation.Status.confirm) {
      this.agvStatusLogService.delete(id).subscribe(() => this.list.get());
    }
  });
}
}
