<div class="card">
  <div class="card-header">
    <div class="row">
      <div class="col col-md-6">
        <h5 class="card-title">
          {{ '::Menu:AGVStatusLog' | abpLocalization }}
        </h5>
      </div>
      <div class="text-end col col-md-6"></div>
    </div>
  </div>
  <div class="card-body">
    <ngx-datatable [rows]="agvStatusLog.items" [count]="agvStatusLog.totalCount" [list]="list" default>
      <ngx-datatable-column [name]="'::AGVID' | abpLocalization" prop="agvid"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::BatteryLevel' | abpLocalization" prop="batteryLevel"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::OperationStatus' | abpLocalization" prop="operationStatus"></ngx-datatable-column>
      <ngx-datatable-column [name]="'::ErrorMessage' | abpLocalization" prop="errorMessage"></ngx-datatable-column>
      <!-- <ngx-datatable-column [name]="'::Type' | abpLocalization" prop="type">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ '::Enum:BookType.' + row.type | abpLocalization }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::PublishDate' | abpLocalization" prop="publishDate">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.publishDate | date: 'dd/MM/yyyy HH:mm:ss' }}
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column [name]="'::Price' | abpLocalization" prop="price">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.price | currency }}
        </ng-template>
      </ngx-datatable-column> -->
    </ngx-datatable>
  </div>
</div>
